<?php

// do this again.
maybe_set_dev_error_reporting();

// this comes from the MU-plugins inc.php.  //after this will be action: muplugins_loaded
// can use my fancy funcs here... they aint loaded.
$dir = dirname(realpath(__FILE__));
$dir = str_replace('\\', '/', $dir);// for windoze
$dir = rtrim($dir, '/').'/';
$url = str_replace(constant('sdr'), '', $dir);
$url = '/'.trim($url, '/').'/';
define('pathto_inc', $dir);
define('urlto_inc', $url);
define('urlto_incimg', $url.'_img/');
//quickd(pathto_inc, 'pathto_inc', [__FILE__, __LINE__, __FUNCTION__]);
//quickd(urlto_inc, 'urlto_inc', [__FILE__, __LINE__, __FUNCTION__]);


if(function_exists('d_wp_config')){//do stuff in wp-config.php
	d_wp_config();
}

// as needed include_once 'list_hooks.php';
// as needed//include_once 'list_actions.php';

define('pathtoredux', pathto_inc.'/redux/');
$pathtoredux = pathtoredux.'ReduxCore/framework.php';

// include the plugins, here:  ---------------------------------------------------------------------------------------------------------

$plugins = [//
			'd_global/d_global..php',// do me firstly
			'd_form/d_form..php',
			['redux/ReduxCore/framework.php', 'ReduxFramework', $pathtoredux],
			'd_siteSettings/d_siteSettings..php',
			// no more, we're using the plugin like normal. 'metabox.io/meta-box.php',

			'd_admin/d_admin..php',

			'd_shortcodes/d_shortcodes..php',

			'd_gallery/d_gallery..php',
			'd_mobileMenu/d_mobileMenu..php',

			'_misc/_inc_misc.php',

			//'list_hooks.php',
			//'list actions.php',
			//'list_funcs_for_hook.php',


];
if(defined('d_router_2') && d_router_2 === true){
	$plugins[] = 'd_router_2/d_router_2..php';
}else{
	$plugins[] = 'd_router/d_router..php'; //load this last, so we have the path constants.]
}
//quickd($plugins, '$plugins');

//quickd($plugins, '$plugins -- ', [__FILE__, __LINE__, __FUNCTION__]);
foreach($plugins as $plugin){
	if(is_array($plugin)){
		if(isset($plugin[2])){
			$path = $plugin[2];
		}else{
			$path = pathto_inc.$plugin[0];
		}
		if( !class_exists($plugin[1])){
			require_once $path;
		}
	}else{
		$path = pathto_inc.$plugin;
		//quickd($path, '$path', [__FILE__, __LINE__, __FUNCTION__]);
		//wp_register_plugin_realpath( $path );// dont use this...
		require_once $path;
	}

	// Add this line to ensure mu-plugins subdirectories can be symlinked -- havent needed this... or know if it works.
	//wp_register_plugin_realpath( $path );
	// blah, this doesnt work for MU...
}

//action: muplugins_loaded

//pretty sure this is old crap for local dev, with symlinks
/* add_filter('redux/_url', // for symlinkd, must come before include. redux doesnt use plugins url the realpath thing doesnt work for this.  i think this is for local dev.
	function($url){
		$pattern     = '/wp-content(.*)mu-plugins/i';// wp-content/C:/Users/<USER>/Documents/_websites/_addglobal/mu-plugins
		$replacement = 'wp-content/mu-plugins';
		$u           = preg_replace($pattern, $replacement, $url);//d($u);
		return $u;
	}, 1, 1);*/

// tryna fuck with the die handler...
/* function d_wp_die_handler($message, $title = '', $args = []){
	//echo '<BR><pre> '.$message.' <br>';
	//echo '<BR><pre> no die - i am in mu plugins inc. <br>';
	echo "<BR>d_wp_die_handler . errorrr...<!-- {$message}  no die - i am in mu plugins inc -->";
}
add_filter('wp_die_handler', 'd_wp_die_handler', 99);
//define( 'WP_DISABLE_FATAL_ERROR_HANDLER', true ); might need to be in wp-config, but the ad_filter die handler seems to work ok.
*/

//this is really only good for troubleshooting.
/*if(dugdebug()){
	if(!is_admin()){// this fixes the plugins not working issue.
		// Intercept the option "active_plugins" content
		//https://www.satollo.net/dynamically-disable-a-plugin
		add_filter('option_active_plugins', function($plugins){
			foreach($plugins as $i => $plugin){
				if($plugin == 'wp-responsive-menu-pro/wp-responsive-menu-pro.php'){//we're only keeping this cuz it inits jquery todo do jquery someother way.
					continue;
				}
				d($plugin, '$plugin');
				unset($plugins[$i]);//this disables the plugin
			}
			ddie();
			return $plugins;
		});
	}
}*/

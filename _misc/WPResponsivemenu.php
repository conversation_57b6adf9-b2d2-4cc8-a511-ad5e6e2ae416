<?php
// no keep these i think
//remove_action('wp_footer', 'wprmenu_menu', 100);
//remove_action('wp_footer', 'render_wprmenu_menu', 10);//sdaf
//$options = get_option('wprmenu_options'); //d($options['menu_type'], '$options');//custom or normal. // you want custom.  ours will overlay

//add_action('wp_footer', 'd_wprmenu_menu', 10);
function d_wprmenu_menu(){
	echo comment('start D mod menu bar in incs');
	$options = get_option('wprmenu_options');
	// these are the $options.  we can fuck around, if needed.
	/*$ops = ['enabled'                         => '1',
			'menu'                            => '2',
			'search_box_text'                 => 'Search...',
			'bar_title'                       => 'The Enthusiasts',
			'bar_logo_pos'                    => 'left',
			'logo_width'                      => '30',
			'logo_height'                     => '30',
			'logo_link'                       => '/',
			'header_menu_height'              => '42',
			'menu_type'                       => 'default',
			'menu_symbol_pos'                 => 'left',
			'custom_menu_bg_color'            => '#cccccc',
			'menu_icon_animation'             => 'hamburger--slider',
			'slide_type'                      => 'bodyslide',
			'position'                        => 'left',
			'from_width'                      => '768',
			'how_wide'                        => '80',
			'menu_max_width'                  => '400',
			'menu_title_size'                 => '20',
			'menu_title_weight'               => 'normal',
			'menu_font_size'                  => '15',
			'menu_font_weight'                => 'normal',
			'menu_font_text_type'             => 'uppercase',
			'submenu_alignment'               => 'left',
			'sub_menu_font_size'              => '15',
			'sub_menu_font_weight'            => 'normal',
			'sub_menu_font_text_type'         => 'uppercase',
			'cart_contents_bubble_text_size'  => '12',
			'menu_border_top_opacity'         => '0.05',
			'menu_border_bottom_opacity'      => '0.05',
			'menu_bg_size'                    => 'cover',
			'menu_bg_rep'                     => 'repeat',
			'menu_bar_bg_size'                => 'cover',
			'menu_bar_bg_rep'                 => 'repeat',
			'menu_background_overlay_opacity' => '0.83',
			'hide_menubar_on_scroll'          => '1',
			'widget_menu_font_size'           => '28',
			'widget_menu_top_position'        => '10',
			'widget_menu_icon_color'          => '#ffffff',
			'widget_menu_icon_active_color'   => '#ffffff',
			'widget_menu_bg_color'            => '#c82d2d',
			'widget_menu_text_color'          => '#ffffff',
			'widget_menu_open_direction'      => 'left',
			'bar_bgd'                         => '#c92c2c',
			'bar_color'                       => '#ffffff',
			'menu_bgd'                        => '#c82d2d',
			'menu_color'                      => '#ffffff',
			'menu_color_hover'                => '#ffffff',
			'menu_textovrbgd'                 => '#d53f3f',
			'active_menu_color'               => '#ffffff',
			'active_menu_bg_color'            => '#d53f3f',
			'menu_icon_color'                 => '#ffffff',
			'menu_icon_hover_color'           => '#ffffff',
			'menu_border_top'                 => '#ffffff',
			'menu_border_bottom'              => '#ffffff',
			'social_icon_color'               => '#ffffff',
			'social_icon_hover_color'         => '#ffffff',
			'search_icon_color'               => '#ffffff',
			'search_icon_hover_color'         => '#ffffff',
			'google_font_type'                => 'standard',
			'google_font_family'              => 'Arial, Helvetica, sans-serif',
			'google_web_font_family'          => '',
			'menu_icon_type'                  => 'default',
			'custom_menu_font_size'           => '40',
			'custom_menu_icon_top'            => '-7',
			'social_icon_font_size'           => '16'];*/
	$wpr_title      = apply_filters('wpr_title', getSiteTitle());
	$bar_logo       = apply_filters('bar_logo', '/_img/wprm-icon.png');
	$menuburgertext = apply_filters('menuburgertext', 'menu');


	//keep the id for this div
	?>
	<div id="wprmenu_bar" class="d_mod  wprmenu_bar  d_flex_grid">
		<?php
		//<editor-fold desc="-- search box -- ">
		$opt_searchbox_menubar  = ($options['search_box'] == 'menu_bar');
		$searchbox_menubar      = apply_filters('searchbox_menubar', $opt_searchbox_menubar);
		$opt_searchicon_menubar = apply_filters('opt_searchicon_menubar', $options['search_icon']);
		if($searchbox_menubar){ ?>
			<div class="toggle-search"><i class="<?=$opt_searchicon_menubar?>"></i></div>
			<div class="search-expand">
				<div class="wpr_search">
					<?php echo wpr_search_form(); ?>
				</div>
			</div>
		<?php } // if($options['search_box'] == 'menu_bar'
		//</editor-fold> -- search box --
		?>

		<div id="d_wprm_bar_logo">
			<a href="/">
				<img class="bar_logo" alt="logo" src="<?=$bar_logo?>">
			</a>
		</div>

		<div id="d_wprm_menu_title" class=" df_col">
			<span class="wpr_title"><?=$wpr_title?></span>
		</div>

		<div id="d_wprm_hamburger" class="hamburger hamburger--slider">
				<span class="hamburger-box">
				  <span class="hamburger-inner"></span>
				</span>
			<div id="menuburgertext"><?=$menuburgertext?></div>
		</div>

	</div>
	<?php
	echo comment('END D mod menu bar').nl().nl();
}

add_action('wp_footer', function(){
	echo comment('wp_footer action'.__FILE__.' '.__LINE__);
	echo getHashtagIgnorer('nav li.menu-item-has-children > a[href="#"], div.wprm-wrapper li.menu-item-has-children  a.wprmenu_parent_item[href="#"]');
}, 999, 0);

// the hooks
/*
 admin_bar_menu
10	WP_Responsive_Menu->wprmenu_mobile_preview

plugin_action_links_wp-responsive-menu/wp-responsive-menu.php
10	WP_Responsive_Menu->plugin_action_links

plugin_row_meta
10	WP_Responsive_Menu->wprmenu_plugin_row_meta (2)

plugins_loaded
WP_Responsive_Menu->wprmenu_register_strings

wp_ajax_wpr_get_transient_from_data
10	WP_Responsive_Menu->wpr_get_transient_from_data

>>>>>	wp_ajax_wprmenu_import_data
10	WP_Responsive_Menu->wprmenu_import_data

wp_enqueue_scripts
WP_Responsive_Menu->wprmenu_enqueue_styles
	WP_Responsive_Menu->wprmenu_enqueue_scripts

wp_footer
WP_Responsive_Menu->render_wprmenu_menu
	WP_Responsive_Menu->wpr_custom_css*/

//d(wp_get_nav_menu_items('main'), 'wp_get_nav_menu_items() ');
// we are modding to have better control over the header




//add_action('wp_enqueue_scripts', function(){
//	// well, what the fuck.  i guess you have to have this....
//	//wp_deregister_script('touchSwipe');//hafta do both for some stupid reason.
//	//wp_dequeue_script('touchSwipe');
//}, 10000);


//public function wprmenu_enqueue_styles(){
//	wp_register_style('hamburger.css', $this->plugin_url().'/assets/css/wpr-hamburger.css', [], WPRMENU_VERSION);
//	wp_register_style('wprmenu.css', $this->plugin_url().'/assets/css/wprmenu.css', [], WPRMENU_VERSION);
//	wp_register_style('wpr_icons', $this->plugin_url().'/inc/assets/icons/wpr-icons.css', [], WPRMENU_VERSION);
//	wp_enqueue_style('hamburger.css');
//	wp_enqueue_style('wprmenu.css');
//	wp_enqueue_style('wpr_icons');
//	wp_add_inline_style('wprmenu.css', $this->wpr_inline_css());
//}

//public function wprmenu_enqueue_scripts(){
//	wp_register_script('modernizr', $this->plugin_url().'/assets/js/modernizr.custom.js', ['jquery'], WPRMENU_VERSION);
//	wp_register_script('touchSwipe', $this->plugin_url().'/assets/js/touchSwipe.js', ['jquery'], WPRMENU_VERSION);
//	wp_register_script('wprmenu.js', $this->plugin_url().'/assets/js/wprmenu.js', ['jquery', 'touchSwipe'], WPRMENU_VERSION);
//
//	$params = ['zooming'        => $this->option('zooming'),
//			   'from_width'     => $this->option('from_width'),
//			   'push_width'     => $this->option('menu_max_width'),
//			   'menu_width'     => $this->option('how_wide'),
//			   'parent_click'   => ($this->option('parent_click') == 'yes' || $this->option('parent_click') == '1') ? 'yes' : '',
//			   'swipe'          => $this->option('swipe'),
//			   'enable_overlay' => $this->option('enable_overlay'),];
//	wp_localize_script('wprmenu.js', 'wprmenu', $params);
//	wp_enqueue_script('modernizr');
//	wp_enqueue_script('touchSwipe');
//	wp_enqueue_script('wprmenu.js');
//}

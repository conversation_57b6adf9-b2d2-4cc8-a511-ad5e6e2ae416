<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\ChromeManagement;

class GoogleChromeManagementV1NetworkDevice extends \Google\Model
{
  /**
   * @var string
   */
  public $iccid;
  /**
   * @var string
   */
  public $imei;
  /**
   * @var string
   */
  public $macAddress;
  /**
   * @var string
   */
  public $mdn;
  /**
   * @var string
   */
  public $meid;
  /**
   * @var string
   */
  public $type;

  /**
   * @param string
   */
  public function setIccid($iccid)
  {
    $this->iccid = $iccid;
  }
  /**
   * @return string
   */
  public function getIccid()
  {
    return $this->iccid;
  }
  /**
   * @param string
   */
  public function setImei($imei)
  {
    $this->imei = $imei;
  }
  /**
   * @return string
   */
  public function getImei()
  {
    return $this->imei;
  }
  /**
   * @param string
   */
  public function setMacAddress($macAddress)
  {
    $this->macAddress = $macAddress;
  }
  /**
   * @return string
   */
  public function getMacAddress()
  {
    return $this->macAddress;
  }
  /**
   * @param string
   */
  public function setMdn($mdn)
  {
    $this->mdn = $mdn;
  }
  /**
   * @return string
   */
  public function getMdn()
  {
    return $this->mdn;
  }
  /**
   * @param string
   */
  public function setMeid($meid)
  {
    $this->meid = $meid;
  }
  /**
   * @return string
   */
  public function getMeid()
  {
    return $this->meid;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleChromeManagementV1NetworkDevice::class, 'Google_Service_ChromeManagement_GoogleChromeManagementV1NetworkDevice');

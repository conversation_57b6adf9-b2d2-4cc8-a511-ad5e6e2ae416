<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class ResourcePolicyDailyCycle extends \Google\Model
{
  /**
   * @var int
   */
  public $daysInCycle;
  /**
   * @var string
   */
  public $duration;
  /**
   * @var string
   */
  public $startTime;

  /**
   * @param int
   */
  public function setDaysInCycle($daysInCycle)
  {
    $this->daysInCycle = $daysInCycle;
  }
  /**
   * @return int
   */
  public function getDaysInCycle()
  {
    return $this->daysInCycle;
  }
  /**
   * @param string
   */
  public function setDuration($duration)
  {
    $this->duration = $duration;
  }
  /**
   * @return string
   */
  public function getDuration()
  {
    return $this->duration;
  }
  /**
   * @param string
   */
  public function setStartTime($startTime)
  {
    $this->startTime = $startTime;
  }
  /**
   * @return string
   */
  public function getStartTime()
  {
    return $this->startTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ResourcePolicyDailyCycle::class, 'Google_Service_Compute_ResourcePolicyDailyCycle');

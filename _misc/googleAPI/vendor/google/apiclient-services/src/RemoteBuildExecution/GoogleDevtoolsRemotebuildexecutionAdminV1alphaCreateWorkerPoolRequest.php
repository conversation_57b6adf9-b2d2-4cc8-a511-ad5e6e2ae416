<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\RemoteBuildExecution;

class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateWorkerPoolRequest extends \Google\Model
{
  public $parent;
  public $poolId;
  protected $workerPoolType = GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool::class;
  protected $workerPoolDataType = '';

  public function setParent($parent)
  {
    $this->parent = $parent;
  }
  public function getParent()
  {
    return $this->parent;
  }
  public function setPoolId($poolId)
  {
    $this->poolId = $poolId;
  }
  public function getPoolId()
  {
    return $this->poolId;
  }
  /**
   * @param GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool
   */
  public function setWorkerPool(GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool $workerPool)
  {
    $this->workerPool = $workerPool;
  }
  /**
   * @return GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool
   */
  public function getWorkerPool()
  {
    return $this->workerPool;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateWorkerPoolRequest::class, 'Google_Service_RemoteBuildExecution_GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateWorkerPoolRequest');

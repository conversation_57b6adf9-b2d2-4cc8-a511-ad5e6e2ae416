<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\ShoppingContent;

class AccountImageImprovements extends \Google\Model
{
  protected $accountImageImprovementsSettingsType = AccountImageImprovementsSettings::class;
  protected $accountImageImprovementsSettingsDataType = '';
  /**
   * @var bool
   */
  public $effectiveAllowAutomaticImageImprovements;

  /**
   * @param AccountImageImprovementsSettings
   */
  public function setAccountImageImprovementsSettings(AccountImageImprovementsSettings $accountImageImprovementsSettings)
  {
    $this->accountImageImprovementsSettings = $accountImageImprovementsSettings;
  }
  /**
   * @return AccountImageImprovementsSettings
   */
  public function getAccountImageImprovementsSettings()
  {
    return $this->accountImageImprovementsSettings;
  }
  /**
   * @param bool
   */
  public function setEffectiveAllowAutomaticImageImprovements($effectiveAllowAutomaticImageImprovements)
  {
    $this->effectiveAllowAutomaticImageImprovements = $effectiveAllowAutomaticImageImprovements;
  }
  /**
   * @return bool
   */
  public function getEffectiveAllowAutomaticImageImprovements()
  {
    return $this->effectiveAllowAutomaticImageImprovements;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AccountImageImprovements::class, 'Google_Service_ShoppingContent_AccountImageImprovements');

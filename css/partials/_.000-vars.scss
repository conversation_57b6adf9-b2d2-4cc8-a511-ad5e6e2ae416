// --------------------------------------------------------
$bodyBGcolor: $white;
$fontcolor: $dkgrey;
$linkcolor: $dkblue;
$linkover: $ltblue;

$fontColorDkBg: $dkwhite;
$linkcolorDkBg: $dkwhite;
$linkoverDkBg: $white;

$fontColor_header: $fontColorDkBg;
$fontColor_link_header: $fontColorDkBg;
$fontColor_link_header_over: $linkoverDkBg;

$fontColor_footer: $fontColor_header;
$fontColor_link_footer: $fontColor_link_header;
$fontColor_link_footer_over: $fontColor_link_header_over;

$H1_color: $fontcolor;
$H1_link: $fontcolor;
$H1_link_over: $ltblue;
$H1_shadow: 1.5px 1.5px 0 $medblue;

$H1_colorDkBg: $dkwhite;
$H1_linkDkBg: $dkwhite;
$H1_link_overDkBg: $ltgrey;

$h1-size: $h1_ems * $H1_multiplier !default;
$h2-size: $h2_ems * $H1_multiplier !default;
$h3-size: $h3_ems * $H1_multiplier !default;
$h4-size: $h4_ems * $H1_multiplier !default;
$h5-size: $h5_ems * $H1_multiplier !default;
$h6-size: $h6_ems * $H1_multiplier !default;

$H1_topAlign: $H1_topAlign * $H1_multiplier !default;
$H1_margin-bottom: $H1_margin-bottom * $H1_multiplier !default;
$H1_line-height: $H1_line-height * $H1_multiplier !default;

// THING colors ---------------------------------------------------
// gallery
$d_gallery_title_bg: $white;
$d_gallery_gslide-title: $H1_color;
$d_gallery_gslide-desc: $dkgrey;
$d_gallery_masonry_gutter: 1.5%;

$footerBackground: $black;
$headerBackground: $black;

$navcolor: $fontColorDkBg;
$navcolor_over: $linkoverDkBg;

$wprmenu_menubar_background: $black;
$wprmenu_menubar_burger_color: $dkwhite;
$wprmenu_menu_ul: $black;
$wprmenu_menu_li_active: $dkblue;
$main_menu_border: none;
$wprmenu_color: $dkwhite;
$wprmenu_color_over: $dkwhite;

$table_zebra_tr_odd_bg: $ltgrey;
$table_zebra_tr_even_bg: $white;
$table_zebra_th_bg: $medgrey;
$table_zebra_th_color: $black;
$table_zebra_th_borderright: 1px dashed $moregrey; //transparent;


// fonts ----------------------------------------------------------------------------------------
// i dont really know why... seems superflous, but maybe i had a reason.
$font_normal: $sans;
$font_serif: $serif;
$font_mono: $mono;
$font_narrow: $narrow;


$font_header: $serif;
$font_nav: $font_header;

// dimensions --------------------------------------------------------------------
//$nav_wpr_height: 60px;
//$nav_wpr_space: 100px; //this is for adding space, a margin, under the wprmenubar

//$wrapperpx + $wrapperpxBuffer → 1100px + 50 →  becomes 1150px
//1150px + 1 → becomes 1151px
//Since the first value has a unit, Sass is OK with adding unitless numbers to it.
$wrapperpx: 1100px;
$wrapperpxBuffer: 50; //yes, should be just a number.  will be added to whatever.   this just gives a little extra so that if the wrapper is right at the $wrapperpx, it's not touching

$wrapper_padding: 4vw;

$atMedia_ipad_max-width: 768px; // up_to_ipad {								    @media screen and (max-width: $atMedia_ipad_max-width)
$atMedia_ipad_min-width: ($atMedia_ipad_max-width + 1); // more_than_ipad {	@media screen and (min-width: $atMedia_ipad_min-width)

$small_max-width: 550px; //ok if you do this (not in quotes) , you can add +5 to it. orwhatever
$small_min-width: ($small_max-width + 1);

$wrapper_max-width: ($wrapperpx + $wrapperpxBuffer);
$wrapper_min-width: ($wrapper_max-width +1);

// mixins for these on the _beaverbuilder_global.scss
$beaverbuilder_LargeMaxWidth: 1200px; //up_to_large {@media screen and (max-width: $beaverbuilder_LargeMaxWidth)
$beaverbuilder_LargeMinWidth: ($beaverbuilder_LargeMaxWidth + 1); //more_than_large {@media screen and (min-width: $beaverbuilder_LargeMinWidth)

$beaverbuilder_MediumMaxWidth: 992px;
$beaverbuilder_MediumMinWidth: ($beaverbuilder_MediumMaxWidth + 1);

$beaverbuilder_SmallMaxWidth: 768px;
$beaverbuilder_SmallMinWidth: ($beaverbuilder_SmallMaxWidth + 1);


// overlays and SHADOWS -------------------------------------------------------------
$header_shadow: 1.5px 1.5px 0 $medblue;
$textshadow: .5px .5px .5px rgba(0, 0, 0, 0.5);
$transparentWhite01: rgba(255, 255, 255, 0.01);
$transparentWhite05: rgba(255, 255, 255, 0.05);
$transparentWhite6: rgba(255, 255, 255, 0.6);
$transparentWhite8: rgba(255, 255, 255, 0.8);
$overlay: rgba(16, 18, 19, 0.7);


//bak compat ------------------------------------------------------
$border-color: $grey;
$disabled-gray: $ltgrey;
$light-gray: $ltgrey;
$gray: $grey;
$text-color: $dkgrey;
$meta-gray: $dkgrey;
$bones-pink: $pink;
$UnderWrapperSidePadding: $wrapper_padding; // i think this is crap


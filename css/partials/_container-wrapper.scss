//
//
body {
	#body_container {
		// no this has no top margin.@include container;
		//margin: $wrapper_padding auto;
		width: 100%;
		max-width: 2000px;

		#body_wrapper {
			width: 100%;
			max-width: $wrapperpx;
			margin: 0 auto;
			padding: $wrapper_padding 0 0 0;

			@include up_to_wrapper {
				padding: $wrapper_padding $wrapper_padding 0;
			}
		}
	}

	&.fullwidth #body_container {
		//margin: 0;

		#body_wrapper {
			max-width: 100% !important;
			padding: 0 !important;

			@include up_to_wrapper {
				padding: 0 !important;
			}
			@include up_to_ipad {
				padding: 0 !important;
			}
		}

		// idk if this really does what you want.  just cuz there will likely be a single div.. idk.
		.entry-content,
		#post_wrapper,
		div#content {
			& > div:last-child {
				padding-bottom: 0;
			}
		}
	}
}


// so basically, if you have it on fullwidth, you'll be using beaver, which has its own wrapper max width and stuff.
// So, no padding on any of this.
// if you have it on fullwidth, without using beaver,
// you'll need to set width on  <div class='content' or whatever.
//body.fullwidth {
//	#body_container {
//		margin-bottom: 0;
//		margin-top: 0;
//
//		#body_wrapper {
//			max-width: 100% !important;
//			padding: 0 !important;
//
//			@include up_to_wrapper {
//				padding: 0 !important;
//			}
//			@include up_to_ipad {
//				padding: 0 !important;
//			}
//		}
//	}
//
//	div#content > div:last-child {
//		padding-bottom: 0;
//	}
//}
//



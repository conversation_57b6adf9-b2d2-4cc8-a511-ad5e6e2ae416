// this gets hidden, during wprm
//<editor-fold desc="-- header structure -- ">
//<body >
//<div id="headers_container">
//	<div id="mainheader_container" class="">
//		<div id="mainheader_wrapper" class="cf">
//			<div id="logo-and-text">
//				<div id="text">
//					<div id="site-title-text"><a href="/"></div>
//					<div id="site-description"></div>
//				</div>
//			</div>
//			<div id="menu">
//				<nav id="main-menu"></nav>
//			</div>
//		</div>
//	</div>
//</div>
//</editor-fold> -- header structure --

#headers_container {
	width: 100%;
	background-color: $headerBackground;

	#mainheader_container {
		//@media screen and (max-width: ($wrapperpx + $wrapperpxBuffer)) {
		//	padding: 0 $wrapper_padding;
		//}
		@include container;

		#mainheader_wrapper {
			@include wrapper;
			//max-width: $wrapperpx;
			//margin: 0 auto;
			//@include up_to_wrapper {
			//	padding: 0 $wrapper_padding;
			//}

			#logo-and-text {
				float: left;
				margin: 0;

				a {
					@include links($fontColor_link_header, $fontColor_link_header_over, linkDecNo, linkDecNo);
					text-decoration: none !important;
				}
			}

			#menu {
				float: right;
				margin: 16px 0 0 0 !important;
			}
		}
	}

	#mobile_main_header_wrapper {
		background: $wprmenu_menubar_background;
		padding: 0 ($wrapper_padding / 2);
	}
}



body #body_wrapper { // todo temporraryyyyyyyyyyyyyyyyyyyyyyyyyyy
	border-top: 1px solid #ccc;
}

#mobile_menu * {
	text-rendering: optimizeLegibility;
	line-height: 1 !important;
}

#mobile_menu {
	background: $wprmenu_menubar_background;
	//<editor-fold desc="-- menu bar -- ">
	#wprmenu_bar.d_mod {
		background: $wprmenu_menubar_background;

		align-items: center;
		justify-content: space-between;

		margin: 0 !important;
		padding: 3px 0 !important;

		max-width: 100% !important;
		height: auto;

		color: $wprmenu_color;
		font-size: 17px;

		a {
			@include links($wprmenu_color, $wprmenu_color_over !important, linkDecNo, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'
		}

		#d_wprm_bar_logo {
			border: none;
			margin: 0 ($wrapper_padding / 2) 0 0;
			padding: 0;

			img.bar_logo {
				max-width: 100%; /* Image can't be wider than its container this makes it shrink to fit */
				max-height: 50px; // since we're alreadying using max-width, we can use max-height to control the size.
				height: auto; /* Maintain aspect ratio */
				width: auto; /* Let it scale naturally */
			}
		}

		#d_wprm_menu_title {
			width: auto;
			padding: 10px 11px 11px 0;
			text-align: left;
			display: block;
			font-family: $font_nav;
			text-transform: uppercase;
			font-size: 26px;
			color: $fontcolor;
			@media screen and (max-width: 400px) {
				font-size: 18px;
			}
			@media screen and (max-width: 300px) {
				font-size: 14px;
			}
		}


		//<editor-fold desc="-- flex stuffs -- ">
		#d_wprm_bar_logo {
			flex-shrink: 1; /* Allow this to shrink */
			flex-grow: 0; /* Don't grow */
			min-width: 0; /* Allow it to shrink below content size if needed */

		}

		#d_wprm_menu_title {
			flex-shrink: 1; /* Allow this to shrink too */
			flex-grow: 1; /* Let it take up available space */
			min-width: 0; /* Allow it to shrink below content size if needed */
		}

		#d_wprm_hamburger {
			flex-shrink: 0; /* Never shrink */
			flex-grow: 0; /* Don't grow */
			top: 1px;
			position: relative;
		}

		//</editor-fold> -- flex stuffs --


	}

	//</editor-fold> -- menu bar --
}


//<editor-fold desc="-- the actual menu UL: -- ">
.d_wprm-wrapper *, .wprm-wrapper * {
	text-rendering: optimizeLegibility;
}

.d_wprm-wrapper #mg-wprm-wrap, .wprm-wrapper #mg-wprm-wrap { // this is theirs, but we do show it. its the menue
	width: 100% !important;
	max-width: none !important;
	background: $wprmenu_menu_ul;
	top: auto !important;

	#wprmenu_menu_ul {
		background-color: $wprmenu_menu_ul;
		padding: 0;

		// this is the open/close for subs
		.wprmenu_icon {
			text-align: right;
			color: $wprmenu_color;

			&::before {
				color: $wprmenu_color;
			}
		}


		$liborder: 1px solid rgba(68, 68, 68, .4);

		li.menu-item {
			color: $wprmenu_color;
			border-bottom: $liborder;

			&:first-child { //todo this could be temporraryyyyyyyyyyy
				border-top: $liborder;
			}

			a {
				@include links($wprmenu_color !important, $wprmenu_color_over !important, linkDecNo, linkDecNo);
				background-color: transparent; //we need these
				font-family: $font_nav;
				font-weight: bold;
				font-size: 20px;
				text-transform: capitalize;
				padding: 2.5% 0 2.5% 2%;
				display: block;
			}

			a:hover {
				background-color: transparent; //we need these
			}

			&.current-menu-item {
				background-color: $wprmenu_menu_li_active;

				& > a {
					background-color: transparent; //we need these
				}
			}

			ul li:last-child {
				border-bottom: 0;
			}
		}
	}
}

//</editor-fold> -- the actual menu UL: --


//
//
//

// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //
// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //
// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //
// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //
// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //


#wprmenu_bar.d_mod {
	display: flex !important;
	position: static !important;
}

// hider and shower
@include up_to_ipad {
	#mainheader_wrapper {
		display: none;
	}
	#mobile_main_header_wrapper {
		display: block;
	}
}
@include more_than_ipad {
	#mainheader_wrapper {
		display: block;
	}
	#mobile_main_header_wrapper {
		display: none;
	}
}

//this was inline:
@media only screen and (max-width: 768px) {
	// Base HTML adjustments
	html body div {
		&.wprm-wrapper {
			overflow: scroll;
		}

		&#wpadminbar {
			position: fixed;
		}
	}

	// Main menu bar
	#wprmenu_bar {
		background-size: cover;
		background-repeat: repeat;
		background-color: #c92c2c;
		height: 42px;
		display: block;

		.menu_title {
			color: #ffffff;

			a {
				color: #ffffff;
				font-size: 20px;
				font-weight: normal;
			}
		}

		.wprmenu_icon_menu {
			color: #ffffff;
		}
	}


	// Search wrapper
	html body div#mg-wprm-wrap {
		.wpr_submit {
			.icon.icon-search {
				color: #ffffff;
			}
		}
	}

	// Main menu wrapper
	#mg-wprm-wrap {
		background-color: #c82d2d;
		display: block;

		// Menu positioning
		&.cbp-spmenu-right,
		&.cbp-spmenu-left,
		&.cbp-spmenu-right.custom,
		&.cbp-spmenu-left.custom,
		&.cbp-spmenu-vertical {
			width: 80%;
			max-width: 400px;
		}


		// Menu items
		li.menu-item {
			a {
				font-size: 15px;
				text-transform: uppercase;
				font-weight: normal;
				color: #ffffff;

				&:hover {
					background: #d53f3f;
					color: #ffffff !important;
				}
			}

			&:valid ~ a {
				color: #ffffff;
			}

			// Sub-menu items
			&-has-children {
				ul.sub-menu {
					a {
						font-size: 15px;
						text-transform: uppercase;
						font-weight: normal;
					}
				}
			}
		}

		// Current menu item
		li.current-menu-item {
			> a {
				background: #d53f3f;
				color: #ffffff !important;
			}

			span.wprmenu_icon {
				color: #ffffff !important;
			}
		}

		// Menu list styling
		ul {
			&#wprmenu_menu_ul {
				li.menu-item {
					a {
						color: #ffffff;
					}
				}
			}

			> li:hover > span.wprmenu_icon {
				color: #ffffff !important;
			}
		}
	}

	// Menu wrapper icons
	div#mg-wprm-wrap {
		ul li span.wprmenu_icon {
			color: #ffffff;
		}
	}

	// Menu positioning classes
	.cbp-spmenu-push-toright {
		left: 80%;

		.mm-slideout {
			left: 80%;
		}
	}

	.cbp-spmenu-push-toleft {
		left: -80%;
	}

	// Menu panels
	#wprmenu_menu {
		&.left {
			width: 80%;
			left: -80%;
			right: auto;
		}

		&.right {
			width: 80%;
			right: -80%;
			left: auto;
		}
	}

}

//this was in wprmenu.css
html {
	&.wprmenu-body-fixed {
		overflow: inherit;
	}

	&.wprmenu-body-fixed body {
		position: fixed !important;
	}

	body {
		position: relative !important;

		.wprm-overlay.active {
			height: 100%;
			width: 100%;
			z-index: 9999;
			position: fixed;
		}

		div.wprm-wrapper {
			z-index: 999999;
		}
	}
}


// from https://jonsuh.com/hamburgers
// Hamburger menu styling
.hamburger--slider {
	background-color: transparent;
	overflow: visible;

	text-align: center;
	text-transform: none;
	font: inherit;
	color: inherit;

	padding: 0 !important;
	border: 0;
	margin: 0;


	float: left;
	display: inline-block;
	cursor: pointer;

	transition-property: opacity, filter;
	transition-duration: 0.15s;
	transition-timing-function: linear;

	.hamburger-box {
		width: 100%;
		height: 24px;
		display: inline-block;
		position: relative;
	}

	#menuburgertext {
		font-size: 13px;
		text-transform: lowercase;
		text-align: center;
		color: $wprmenu_menubar_burger_color;
		font-family: $font_nav;
	}

	//<editor-fold desc="-- patties -- ">

	.hamburger-inner,
	.hamburger-inner::before,
	.hamburger-inner::after {
		background: $wprmenu_menubar_burger_color;
		height: 5px;
		width: 100%;
		display: block;
		border-radius: 4px;
		position: absolute;
		transition-property: transform;
		transition-duration: 0.15s;
		transition-timing-function: ease;
	}

	.hamburger-inner::before,
	.hamburger-inner::after {
		content: "";
		display: block;
	}

	.hamburger-inner {
		top: 2px;
	}

	.hamburger-inner::before {
		top: 10px;
		transition-property: transform, opacity;
		transition-timing-function: ease;
		transition-duration: 0.15s;
	}

	.hamburger-inner::after {
		top: 20px;
	}

	//</editor-fold> -- patties --


	&.is-active {
		.hamburger-inner {
			transform: translate3d(0, 10px, 0) rotate(45deg);

			&::before {
				transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
				opacity: 0;
			}

			&::after {
				transform: translate3d(0, -20px, 0) rotate(-90deg);
			}
		}
	}

}

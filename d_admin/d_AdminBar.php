<?php

class d_AdminBar{
	public $sessionKey      = 'show_admin_bar';
	public $adminBar_option = 'adminBar_option';


	function __construct(){
		add_action('init', [$this, 'init'], 1, 0);
		add_action('plugins_loaded', [$this, 'plugins_loaded'], 9, 0);

		include 'd_AdminBarSettings-redux.php';// uses d_admin.
		$d_AdminBarSettings = new d_AdminBarSettings($this);
	}

	function init(){
		if(is_admin()){
			return;
		} //i think this is safe...
		if( !isAuthor(/*or above*/)){
			return;
		}// this seems ok.  we are disabling this whole plugin if theres not a high enough user logged in.
		// only ppl high enough need this plugin.
		// this is ok to remove, it was just for performance.  but if you need, ie,  the settings of this
		//  for a general user, you would need to load it, before this check.

		add_action('wp_head', function(){
			if($GLOBALS['artist_header_HeaderGot'] ?? 0){
				return;
			}
			?>
			<style id="d_hideadminbar">
				#wpadminbar #wp-toolbar * .adminbarkillbutt { /* lame specificity here :/ */
					background: #fff none repeat scroll 0 0;
					border: 1px solid #ccc;
					border-radius: 3px;
					color: #000;
					font-size: 11px;
					line-height: 20px;
					padding: 0 11px;
					text-decoration: none;
				}

				#wpadminbar {
					z-index: 999 !important;
				}

			</style><?php
		});
	}

	function plugins_loaded(){
		if(is_admin()){
			return;
		} //i think this is safe...
		$opt = $this->adminBar_option;

		global $$opt;

		$opt = $$opt; //d($opt);// we have globaled the global option var and have our opts, now. cuz this is called on plugins loaded, so the redux has run and all that.
		//d($opt, '$opt');

		$enable            = $opt['enable'];
		$this->hideAtWidth = $opt['hideAtWidth'] ?? 0;// if this dont exist, it will never hide and this is fine.

		if($enable === '0'){
			return;
		}

		$this->showOrHideToolbar();
		//wp_footer
		add_action('wp_footer', function(){
			if($GLOBALS['artist_header_HeaderGot']){
				return;
			}
			global $d_AdminBar;                                 //d($d_AdminBar, '$d_AdminBar');
			$width = trim(trim($d_AdminBar->hideAtWidth, 'px'));// trim off px, if exist
			if($width < 1 and $d_AdminBar->adminBarShow){
				return;
			}// has to be the instance, not $this.  Omnis server is the first and only time i've seen this problem...
			echo comment(' start output func: d_adminBar plugins_loaded() ');
			?>
			<style id="noAdminBarResponsive_func_filter">
				@media screen and (max-width: <?=$width?>px) {
					html {
						margin-top: 0 !important;
					}

					html #wpadminbar {
						display: none !important;
					}
				}
			</style>
			<?php
			echo comment(' end outout func: d_adminBar plugins_loaded()');
		}, 1, 0);
	}

	function showRestoreButton(){
		add_action('wp_footer', function(){
			//<editor-fold desc="-- check for return -- ">
			global $d_AdminBar;
			if($GLOBALS['artist_header_HeaderGot'] or !is_user_logged_in() or !isAdministrator() or is_admin_bar_showing()){
				return;
			}
			if(defined('noAdminbuttons') and constant('noAdminbuttons') == 1){
				return;
			}
			//</editor-fold> -- check for return --
			$newu = addGetParams([$d_AdminBar->sessionKey => '1']);// has to be the instance, not $this.  Omnis server is the first and only time i've seen this problem...

			echo comment('start d_admin_footer div showRestoreButton()');
			//<editor-fold desc="-- style -- ">
			$css = "
			<style>
				.d_admin_footer {
					font-family: arial, sans-serif !important;
					background: #fff !important;
					color: #000 !important;
					position: relative !important;
				}

				#showRestoreButts input[type=button] {
					background: #ccc none repeat scroll 0 0 !important;
					border: 1px solid #999 !important;
					color: #000 !important;
					font-family: arial, sans-serif !important;
					padding: 4px !important;
				}

				#showRestoreButts #note {
					background: #000 !important;
					color: #fff !important;
					width: 100% !important;
					display: block !important;
				}
			</style> ";
			echo $css;
			//</editor-fold> -- style --

			//<editor-fold desc="-- buttons -- ">
			echo div('id="showRestoreButts" class="d_admin_footer" ');
			echo wspan('Only logged in admins see below here:<br>', 'id="note"');

			$button = buttonWithLink('Restore admin bar', $newu, $attribs = '');
			echo wdiv($button, 'id="restoreAdminBarLink" align="center" style="display: inline-block;"');

			//			$button = buttonWithLink('Edit This Post', get_edit_post_link($id, $context), $attribs = '');
			//			echo wdiv($button, 'id="restoreAdminBarLink" align="center" style="display: inline-block;"');

			$button = buttonWithLink('Edit This Post (new window)', get_edit_post_link($id, $context), $attribs = 'target="_blank"');
			echo wdiv($button, 'id="restoreAdminBarLink" align="center" style="display: inline-block;" ');

			//			$button = buttonWithLink('Admin Dashboard', '/wp-admin/index.php', $attribs = '');
			//			echo wdiv($button, 'id="restoreAdminBarLink" align="center" style="display: inline-block;" ');

			$button = buttonWithLink('Admin Dashboard (new window)', '/wp-admin/index.php', $attribs = 'target="_blank"');
			echo wdiv($button, 'id="restoreAdminBarLink" align="center" style="display: inline-block;" ');

			if(function_exists('isPostBeaverBuilder') and isPostBeaverBuilder()){
				//				$button = buttonWithLink('Edit with Beaver Builder', '?fl_builder', $attribs = '');
				//				echo wdiv($button.'<BR>ctrl-p is publish', 'id="editwithbeaverbuilderLink" align="center" style="display: inline-block;" ');
				$button = buttonWithLink('Edit with BB new window--ctrl-p is publish', '?fl_builder', $attribs = 'target="_blank"');
				echo wdiv($button, 'id="editwithbeaverbuilderLink" align="center" style="display: inline-block;" ');
			}

			//</editor-fold> -- buttons --
			echo div(0);
			echo comment('END d_admin_footer div showRestoreButton()');
			//template display and queries and timer is in the d_display_tpl_name.php

		}, 1, 0);
	}


	function showOrHideToolbar(){// you need session_start();  somewhere.  wpconfig
		$this->adminBarShow = true;
		if(isset($_GET[$this->sessionKey]) and $_GET[$this->sessionKey] === '1'){ //d('get 1: show, showkillbutt, return(add sess)');
			$this->showKillButton();
			//$_SESSION[$this->sessionKey] = 1;
			setcookieTo($this->sessionKey, 1);
			$this->adminBarShow = true;
			return;
		}elseif(isset($_GET[$this->sessionKey]) and $_GET[$this->sessionKey] === '0'){
			//d('get 0: dont show, show restore, (add sess) return');
			$this->hideAdminbar();
			$this->showRestoreButton();
			//$_SESSION[$this->sessionKey] = 0;
			setcookieTo($this->sessionKey, 0);
			$this->adminBarShow = false;
			return;
		}

		//d($_COOKIE[$this->sessionKey] );
		//d(stringistrue($_COOKIE[$this->sessionKey]));
		//if((isset($_GET[$this->sessionKey]) and stringistrue($_SESSION[$this->sessionKey])) or stringistrue($_COOKIE[$this->sessionKey])){
		if(stringistrue($_SESSION[$this->sessionKey] ?? false) or stringistrue($_COOKIE[$this->sessionKey] ?? false)){
			$this->showKillButton();
			$this->adminBarShow = true;
		}else{
			$this->hideAdminbar();
			$this->showRestoreButton();
			$this->adminBarShow = false;
		}
	}

	function showKillButton(){
		if($GLOBALS['artist_header_HeaderGot']){
			return;
		}
		add_action('admin_bar_menu', function($wp_admin_bar){// $wp_admin_bar is sent in by the action, it's an object that you can add_node's to.
			global $d_AdminBar;
			$ra   = [$d_AdminBar->sessionKey => '0'];
			$newu = addGetParams($ra);                                                                                                                        //
			$args = ['id' => 'killadminToolbar', 'title' => ' '.buttonNoLink('Hide this toolbar to footer', 'class="adminbarkillbutt"'), 'href' => $newu];    //$args = array('id' => 'killadminToolbar', 'title' => ' '.buttonWithLink('Hide adminToolbar to footer', '/wp-admin/index.php', $attribs = 'class="adminbarkillbutt"'), 'href' => $newu );
			$wp_admin_bar->add_node($args);
		}, 999999);
	}

	function hideAdminbar(){
		add_filter('show_admin_bar', '__return_false', 6);//  ok, wp calls the applyfilter(show_admin_bar) We need to return false, here.
		//Shoulndt use this->sessionkey, cuz we dont want to break this func if we change that key
		//(it was just a coincidence that it was named the same :/  fer fucks sake
	}


}//class ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss





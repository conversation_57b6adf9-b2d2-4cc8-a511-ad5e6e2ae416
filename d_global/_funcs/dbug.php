<?php
//some options:
// dbug_location if theres trouble with getting the right location
// $GLOBALS['dbug_disable']
// $GLOBALS['dbug_public']
// $GLOBALS['dbug_alwaysJS']- print the js on ever dbug instead of just once per page.  might help with emailing or some shit.

//echo d_callStack(debug_backtrace()); -- call this like this, so it has the debug_backtrace() from whence it came.
function d_callStack($stacktrace = false){
	$stacktrace = ($stacktrace) ? : debug_backtrace();//d($stacktrace, '$stacktrace');
	$out        = '';
	foreach($stacktrace as $node){
		if(haystackcontainsneedle($node['file'], ['template-loader', 'dbug', 'index', 'wp-blog-header'], false)){
			continue;
		}// just get rid of shit
		$theargs = dreturn($node['args'], '', ['collapsed' => true, 'location' => basename($node['file'])."(".$node['line']."): ".$node['function'].'()']);
		$out     .= $theargs;
	}
	return wdiv('Stacktrace'.$out, ' class="cf" style="border:1px solid; margin:10px 0; padding:5px;" ');//style="border:1px solid; margin:10px 0; padding:5px;"
}

function dhr($txt = ''){
	if( !dugdebugfunc()){
		return;
	}//if not dug, bail.
	$loc     = ddumpwhereCalled();
	$comment = basename(strip_tags($loc));
	echo hr('', ' title="'.$comment.'" style="height: 3px;background-color: #000;" ').$txt;
}

function rdbr($txt = '', $txt2 = '', $return = true){
	return dbr($txt, $txt2, $return);
}

function dbr($txt = '', $txt2 = '', $args = ''){//$return = false
	if( !dugdebugfunc()){
		return;
	} //if not dug, bail.
	$txt = (is_bool($txt)) ? booltostring($txt) : $txt;
	if(is_bool($args)){
		$return         = $args;
		$args           = [];
		$args['return'] = $args;
	}//must have been old, sending the old 'return' param

	extract(wp_parse_args($args, ['location' => ddumpwhereCalled(),
								  'return'   => false]));
	$loc      = $location;
	$comment  = basename(strip_tags($loc));
	$divstyle = 'font-family:\'Consolas\', monospace; font-size: 12px; background: #d5d1d1;  display: block !important;  clear: both;  	color: black  !important; padding: 2px; border: 1px solid #999';
	if( !empty($txt2)){
		$cs   = wdiv($txt2.': '.wstrong($txt).wspan($comment, ' style="font-size:9px;color:#767373 !important;" '), ' style="'.$divstyle.'" ');
		$atss = ' title="'.$comment.'" class="clearfix cf" style="clear:both"  ';
		$out  = wdiv($cs, $atss);//style="max-width:500px"
		if( !$return){
			echo $out;
		}
		return $out;
	}

	$out = wdiv($txt.wspan($comment, ' style="font-family:\'Consolas\', monospace;font-size:12px;color:#9e9d9d;" '), '   clear="all" class="cf" style="'.$divstyle.'"   ');
	if( !$return){
		echo $out;
	}
	return $out;
}

function devecho($str = '', $args = []){
	if( !is_dev()){
		return;
	}
	echo $str;
}

function dfileexists($file, $note = ''){
	$loc              = ddumpwhereCalled();
	$args['location'] = $loc;
	d($file, $note.' file_exists(): '.boolToColor(file_exists($file)), $args);
}

/*function dpre($str, $args = []){
	$loc  = ddumpwhereCalled();
	$args = wp_parse_args($args, ['location' => $loc, 'collapsed' => false]);// fuck me collapsed dont work on pre.
	d(wpre($str), $args);
}*/
function dpre($sql, $txt2 = '', $args = ''){//$return = false
	if( !dugdebugfunc()){
		return;
	} //if not dug, bail.

	extract(wp_parse_args($args, ['location' => ddumpwhereCalled(),
								  'return'   => false]));
	$loc     = $location;
	$comment = basename(strip_tags($loc).' '.$txt2);

	$out = wdiv($sql, '  title="'.$comment.'"  clear="all" class="cf" style="overflow: auto; margin:3px 0; display: block !important;clear: both;white-space: pre;unicode-bidi: embed;font-family: monospace !important;background: #f7f4f4;padding: 0 3px;border: dashed 1px #ccc;"   ');
	if( !$return){
		echo $out;
	}
	return $out;
}

//getFormattedSql($sql){
//getFormattedColoredSql
function dsqlc($sql, $txt2 = '', $args = ''){
	$args = wp_parse_args($args, ['location' => ddumpwhereCalled(),
								  'colored'  => true]);
	return dsql($sql, $txt2, $args);
}

function dsql($sql, $txt2 = '', $args = ''){
	if( !dugdebugfunc()){
		return;
	} //if not dug, bail.
	if(strlen($sql) > 1400){
		echo comment('sql too long, not dumping');
		if( !$return){
			dpre($sql, 'sql too long, dpre()');
		}
		return $sql;
	}
	extract(wp_parse_args($args, ['location' => ddumpwhereCalled(),
								  'return'   => false,
								  'colored'  => false]));
	$loc     = $location;
	$comment = basename(strip_tags($loc).' '.$txt2);
	if($colored){
		$sql = getFormattedColoredSql($sql);
	}else{
		$sql = getFormattedSql($sql);
	}
	//d($sql, '$sql');
	$out = wdiv($sql, '  title="'.$comment.'"  clear="all" class="cf" style="overflow: auto; margin:3px 0; display: block !important;clear: both;white-space: pre;unicode-bidi: embed;font-family: monospace !important;background: #f7f4f4;padding: 0 3px;border: dashed 1px #ccc;"   ');
	if( !$return){
		echo $out;
	}
	return $out;
}

function dCollapsed($var, $note = false, $args = []){
	$loc  = ddumpwhereCalled();
	$args = wp_parse_args($args, ['location' => $loc, 'collapsed' => true]);
	d($var, $note, $args);
}

function dpublic($var, $note = false, $args = []){
	if(is_array($note) and empty($args)){
		$args = $note;
		$note = false;
	}
	$args = wp_parse_args($args, ['public' => true]);
	d($var, $note, $args);
}

function dnoheader_return($var, $note = false, $args = []){
	$args = wp_parse_args($args, ['alwaysJS'    => false,
								  'collapsed'   => false,
								  'public'      => false,
								  'forceType'   => '',//could be 'array' to force xml to array
								  'location'    => ddumpwhereCalled(),// this is correct.  need to get this first. and send it in
								  'disable'     => false,
								  'showheaders' => 0]);
	ob_start();
	dnoheader($var, $note, $args);
	return ob_get_clean();
}

function dnoheader($var, $note = false, $args = []){
	//d($var, '$var');
	$args = wp_parse_args($args, ['alwaysJS'    => false,
								  'collapsed'   => false,
								  'public'      => false,
								  'forceType'   => '',//could be 'array' to force xml to array
								  'location'    => ddumpwhereCalled(),// this is correct.  need to get this first. and send it in
								  'disable'     => false,
								  'showheaders' => 0]);
	d($var, $note, $args);
}

function dcount($var, $note = '', $args = []){
	if( !$var){
		$var = [];
	}
	$args = wp_parse_args($args, ['location' => ddumpwhereCalled(),
								  'return'   => false]);
	dbr(count($var), $note, $args);//	d(count($var), $note, $args);
}

function dso($so, $note = false, $args = []){//shopify order
	$args = wp_parse_args($args, ['location' => ddumpwhereCalled()// this is correct.  need to get this first. and send it in
	]);
	d(simplifyShopifyOrder($so), $note, $args);
}

function dorder($orderOrOID, $note = false, $args = []){
	$args = wp_parse_args($args, ['location' => ddumpwhereCalled()// this is correct.  need to get this first. and send it in
	]);
	d(simplifyOrder($orderOrOID, ['showCart' => true]), $note, $args);
}

function d_imploder($var){
	if( !is_array($var)){
		return $var;
	}
	$var = arraytrimelements($var);
	return wstrong(count($var)).' - \''.@implode("' , '", $var)."'";
}

function dlistOLinks($oids, $note = false, $args = []){
	foreach((array)$oids as &$oid){
		$l       = x_createOrderIDLink($oid);
		$links[] = $l;
	}
	$args['location'] = ddumpwhereCalled();
	return dlist($links, $note, $args);
}

function dlist($var, $note = false, $args = []){
	$args = wp_parse_args($args, ['location' => ddumpwhereCalled()]);// this is correct.  need to get this first. and send it in

	$var = (array)$var;
	$c   = count($var);
	if($args['quoted']){
		$var = implode_quoted($var);
	}else{
		$var = d_imploder($var);
	}

	return d($var, $note.' #'.$c, $args);
}

function dlistQuoted($var, $note = false, $args = []){
	$args = wp_parse_args($args, ['location' => ddumpwhereCalled(), 'quoted' => true]// this is correct.  need to get this first. and send it in
	);
	return dlist((array)$var, $note, $args);
}

function d($var = '', $note = false, $args = []){
	/* could do this...
	if(is_numeric($var) or is_string($var)){
		$var = trim($var);
		return dbr($var,$note,[]);
	}*/

	if(is_array($note) and empty($args)){
		$args = $note;
		$note = false;
	}
	//
	$args = wp_parse_args($args, ['alwaysJS'    => false,
								  'collapsed'   => false,
								  'public'      => false,
								  'forceType'   => '',//could be 'array' to force xml to array
								  'location'    => ddumpwhereCalled(),// this is correct.  need to get this first. and send it in
								  'disable'     => false,
								  'showheaders' => true,
								  'sort'        => false,
								  'return'      => false]);
	extract($args);    //ddump($args);
	$GLOBALS['dbug_disable'] = $GLOBALS['dbug_disable'] ?? false;
	//quickd($GLOBALS['dbug_disable'], '$GLOBALS[dbug_disable] -- ', [__FILE__, __LINE__, __FUNCTION__]);
	if($disable or (bool)$GLOBALS['dbug_disable']){
		echo '<!-- dbug disabled '.__FILE__.' '.__LINE__.' -->';
		return;
	}

	$alwaysJS = $GLOBALS['dbug_alwaysJS'] ?? false;
	$dbugfunc = dugdebugfunc($public);
	if( !$dbugfunc){//if not dug, bail.  unless public==true
		echo '<!-- dbug dugdebugfunc disabled '.__FILE__.' '.__LINE__.' -->';
		//quickd($public, '$public -- ', [__FILE__, __LINE__, __FUNCTION__]);
		//quickd('why not', 'why not -- ', [__FILE__, __LINE__, __FUNCTION__]);
		return;
	}
	if($note){
		$location = $location.' - '.$note;
	} //ddump($location);
	if($return){
		ob_start();
		$dbug = new dbug($var, $note, $location, $alwaysJS, $collapsed, $showheaders, $sort);
		return ob_get_clean();
	}

	$dbug = new dbug($var, $note, $location, $alwaysJS, $collapsed, $showheaders, $sort);//$var,$note=false,$forceType="",$bCollapsed=false,$alwaysJS = false)
	return $dbug->thisvar;                                                               //this makes it actually return the var you sent in, allong with displaying the debug.  nbd.
}

/*  LAST UPDATE March 22, 2007   http://dbug.ospinto.com
if the optional "forceType" string is given, the variable supplied to the  function is forced to have that forceType type.
example: new dBug( $myVariable , "array" );
will force $myVariable to be treated and dumped as an array type,  even though it might originally have been a string type, etc.
forceType is REQUIRED for dumping an xml string or xml file
like: new dBug ( $strXml, "xml" ); */

#[AllowDynamicProperties]
class dBug{
	var $xmlDepth     = [];
	var $xmlCData;
	var $xmlSData;
	var $xmlDData;
	var $xmlCount     = 0;
	var $xmlAttrib;
	var $xmlName;
	var $arrType      = ["array", "object", "resource", "boolean"];
	var $bInitialized = false;
	var $bCollapsed   = false;
	var $arrHistory   = [];
	var $thisvar      = null;
	var $showheaders  = true;
	var $sort         = true; // Add this property declaration

	//constructor
	// dBug($var,$note=false,$forceType="",$bCollapsed=false,$alwaysJS = false, $location ="")
	//function dBug($var, $note = false, $location = "", $alwaysJS = false, $bCollapsed = false, $showheaders = true, $sort = true)
	// constructor same name as class is deprecated to where it just does nothing.
	function __construct($var, $note = false, $location = "", $alwaysJS = false, $bCollapsed = false, $showheaders = true, $sort = true){
		//include js and css scripts
		$this->thisvar     = $var;
		$this->note        = $note;
		$this->location    = $location;
		$this->showheaders = $showheaders;
		$this->sort        = $sort;
		$this->bCollapsed  = $bCollapsed;

		if($alwaysJS){
			echo "<!-- always js -->";
			$this->initJSandCSS();
		}elseif( !defined('BDBUGINIT')){
			echo "<!-- defining bdbuginit -->";
			define("BDBUGINIT", true);
			$this->initJSandCSS();
		}
		//var_dump(ddumpwhereCalled());// no good, here.
		echo nl().nl().'<!-- d()- '.basename(strip_tags($location)), ' -->';//location has spans from the func. just strip em.
		echo nl().'<div class="dbugdiv" style="clear:both;" >'.nl();

		$forceType = $forceType ?? [];
		if(in_array($forceType, ["array", "object", "xml"])){
			$this->{"varIs".ucfirst($forceType)}($var);
		}  //array of variable types that can be "forced"
		else{
			$this->checkType($var);
		}
		echo nl().'</div><!-- end d() clearboth -->'.nl().nl();
	}

	function getOrderarray(){
		$orderarray = ['OrderID', 'link', 'archived', 'OrderNotes', 'OrderDate', 'shipdate', 'shipDate', 'meta', 'cart', 'shopifyOrder', 'ShipStationOrder'];
		$orderarray = array_merge($orderarray, ['sku', 'Quantity', 'QtyFulfilled', 'ProdName', 'ProdAutoID', 'Size', 'Color', 'SizeID', 'ColorID', 'ProdTypeID', 'ArtistID', 'NonStandardCharge']);
		$orderarray = array_merge($orderarray, ['id', 'contact_email', 'fulfillment_status', 'financial_status', 'created_at', 'order_number', 'shipping_address', 'updated_at', 'line_items', 'fulfillments', 'number']);//shopi order
		$orderarray = array_merge($orderarray, ['name', 'title', 'variant_title', 'id', 'sku', 'product_id', 'variant_id', 'quantity', 'fulfillable_quantity', 'fulfillment_service', 'fulfillment_status']);             //shopi prod
		$orderarray = array_merge($orderarray, ['orderNumber', 'orderId', 'orderDate', 'orderStatus', 'createDate', 'modifyDate', 'items', 'shipTo']);                                                                    //ss order
		$orderarray = array_merge($orderarray, ['shipDateStart', 'shipDateEnd']);                                                                                                                                         //misc

		$this->orderarray = $orderarray;
		return $this->orderarray;
	}

	//get variable name it doesnt work so we're using location... ??  Var name is pretty well impossiblle fucki
	//function getVariableName() {

	//create the main table header
	function makeTableHeader($type, $header, $colspan = 2){
		//d($header);
		//d($this->bInitialized);

		if( !$this->bInitialized){
			$header = $this->location." - ".$header." ";
			//$header = $header . " - " .  $this->location. " ";
			$this->bInitialized = true;
		}
		$str_i = ($this->bCollapsed) ? "style=\"font-style:italic\" " : "";

		$showheaders_tr = ' style="';
		if( !$this->showheaders){
			$showheaders_tr .= 'display:none !important;';
		}else{
			$showheaders_tr .= 'display:table-row !important;';
		}
		$showheaders_tr .= '" ';

		echo "\n <table cellspacing=2 cellpadding=3 class=\"makeTableHeader dBug dBug_".$type."\">
				<tr {$showheaders_tr}>
					<td ".$str_i."class=\"dBug_".$type."Header \" colspan=".$colspan." onClick='dBug_toggleTable(this)'>".$header."</td>
				</tr>";
	}

	function makeTDHeader($type, $header){
		if( !$this->showheaders and is_int($header)){
			$header = '';
		}
		$str_d = ($this->bCollapsed) ? ' style="display:none" ' : '  class="expandedd" ';
		echo "<tr".$str_d." {$showheaders_tr}>
				<td valign=\"top\" onClick='dBug_toggleRow(this)' class=\"makeTDHeader dBug_".$type."Key\">".$header."</td>
				<td>";
	}

	function closeTDRow(){
		return "</td></tr>\n";
	}

	function error($type){
		$error = "Error: Variable cannot be a";
		// this just checks if the type starts with a vowel or "x" and displays either "a" or "an"
		if(in_array(substr($type, 0, 1), ["a", "e", "i", "o", "u", "x"])){
			$error .= "n";
		}
		return ($error." ".$type." type");
	}

	function checkType($var){
		//ddump(gettype($var));
		switch(gettype($var)){
			case "resource":
				$this->varIsResource($var);
				break;
			case "object":
				$this->varIsObject($var);
				break;
			case "array":
				$this->varIsArray($var);
				break;
			case "NULL":
				$this->varIsNULL();
				break;

			case "integer":
				$this->varIsInt($var);
				break;

			case "double":
			case "float":
				$this->varIsNumeric($var);
				break;

			case "boolean":
				$this->varIsBoolean($var);
				break;
			case "string":
				$this->varIsString($var);
				break;
			default:
				$this->varIsString($var);

				break;
		}
	}

	function varIsInt($var){
		//echo table();
		$this->makeTableHeader("array", "int: <strong style=\"font-size:110%;background: #fff;padding: 2px;\">{$var}</strong>");
		echo table(0);
	}

	function varIsNumeric($var){
		$this->makeTableHeader("array", "Numeric: <strong style=\"font-size:110%;background: #fff;padding: 2px;\">{$var}</strong>");
		echo table(0);
	}

	function varIsString($var){
		//echo table();
		//echo wh4('is string');
		$this->makeTableHeader("array", "String");
		$var       = ($var == "") ? "[empty string]" : $var;
		$collapsed = ($this->bCollapsed) ? ' style="display:none" ' : '  class="expandedd" ';
		$tdstyle   = ($var == '[empty string]') ? ' style="font-size:9px !important" ' : '';
		echo "<tr {$collapsed} >\n<td $tdstyle >".$var."</td>\n</tr>";
		echo table(0);
	}

	function varIsNULL(){
		//echo table();
		$this->makeTableHeader("array", "null");
		echo table(0);
	}

	function varIsBoolean($var){
		if($var){
			$var = "TRUE";
			$cc  = "#0FC";//0FC = green
		}else{
			$var = "FALSE";
			$cc  = "red";
		}
		//echo table();
		$this->makeTableHeader("array", '<span  style="background-color:'.$cc.' !important;" >'.$var.'</span>');
		//echo $var;style=\"background: $cc !important;\"
		//echo "<table cellspacing=0 border='1' class=\"dBug_array\"><tr>\n<td style=\"background-color: $cc !important;\" ><span style='color:#aaa;'>bool:</span> ".$var."</td>\n</tr>\n</table>\n";
		//echo "<tr>\n<td  style=\"background-color: $cc !important;\"  >".booltostring($var)."</td>\n</tr></table>\n";
		echo table(0);
	}

	function varIsArray($var){
		if($this->sort){
			ksort($var, SORT_NATURAL | SORT_FLAG_CASE);     //dug
			$var = ArbiSorter($var, $this->getOrderarray());//dug
		}
		if(is_array($var)){
			//fucking closures.
			//		if(is_closure($var)){
			//			echo var_dump($var);
			//			ddie('closure');
			//		}
			//		if(is_closure2($var)){
			//			echo var_dump($var);
			//			ddie('closure2');
			//		}
			//		if(is_array($var)){
			//			try{
			//				//ddie($asdfsdaf, '$asdfsdaf');
			//				$reflection = new ReflectionFunction($var);
			//				$rfcl       = new ReflectionClass($var);
			//				$cname      = $rfcl->getName();
			//				var_export($cname);
			//				if($reflection->isClosure()){
			//					//Trigger a E_USER_NOTICE if you want to avoid silently failing
			//					trigger_error("You cannot pass a closure as the second parameter of set()");
			//					return; // Do nothing else
			//				}
			//			}
			//			catch(Exception  $e){// Catch the exception thrown if $value is not a closure
			//				//echo var_dump($var);
			//				ddie('closure catch');
			//			}
			//		}
			// end clusure fucker

			// this works.
			try{
				$var_ser = serialize($var);
			}
			catch(Exception $e){
				//echo 'Caught exception: ', $e->getMessage(), "\n";
			}

			array_push($this->arrHistory, $var_ser);
		}

		$count = count($var);
		$this->makeTableHeader("array", "<span class='racount' >ra#: $count</span>");
		if(is_array($var)){
			foreach($var as $key => $value){
				$this->makeTDHeader("array", $key);

				//check for recursion
				if(is_array($value)){
					$var_ser = serialize($value);
					if(in_array($var_ser, $this->arrHistory, true)){
						$value = "*RECURSION*";
					}
				}

				if(in_array(gettype($value), $this->arrType)){
					$this->checkType($value);
				}else{
					$value   = (trim($value) == "") ? "[empty string]" : $value;
					$tdstyle = ($value == '[empty string]') ? ' style="font-size:9px !important" ' : '';
					echo wspan($value, $tdstyle);
				}
				echo $this->closeTDRow();
			}
		}else{
			echo "<tr><td>".$this->error("array").$this->closeTDRow();
		}
		array_pop($this->arrHistory);
		echo "</table>";
	}

	function varIsObject($var){
		$var_ser = serialize($var);
		array_push($this->arrHistory, $var_ser);
		//	$count = count($var);
		//	$this->makeTableHeader("array", "<span class='racount' >ra#: $count</span>");
		$objvars = count(get_object_vars($var));
		$this->makeTableHeader("object", "<span class='racount' >ct#: $objvars</span>");

		if(is_object($var)){
			$arrObjVars = get_object_vars($var);
			if($this->sort){
				ksort($arrObjVars, SORT_NATURAL | SORT_FLAG_CASE);            //dug
				$arrObjVars = ArbiSorter($arrObjVars, $this->getOrderarray());//dug
			}
			foreach($arrObjVars as $key => $value){
				$value = ( !is_object($value) && !is_array($value) && @trim($value) == "") ? "[empty string]" : $value;
				$this->makeTDHeader("object", $key);

				//check for recursion
				if(is_object($value) || is_array($value)){
					$var_ser = serialize($value);
					if(in_array($var_ser, $this->arrHistory, true)){
						$value = (is_object($value)) ? "*RECURSION* -> $".get_class($value) : "*RECURSION*";
					}
				}
				if(in_array(gettype($value), $this->arrType)){
					$this->checkType($value);
				}else{
					echo $value;
				}
				echo $this->closeTDRow();
			}
			/* dont show functions methods...........  dont show functions methods...........  dont show functions methods...........  dont show functions methods...........  dont show functions methods...........
			$arrObjMethods = get_class_methods(get_class($var));
			foreach($arrObjMethods as $key => $value){
				$this->makeTDHeader("object", $value);
				echo "[function]".$this->closeTDRow();
			}*/
		}else{
			echo "<tr><td>".$this->error("object").$this->closeTDRow();
		}
		array_pop($this->arrHistory);
		echo "</table>";
	}

	function varIsResource($var){
		$this->makeTableHeader("resourceC", "resource", 1);
		echo "<tr>\n<td>\n";
		switch(get_resource_type($var)){
			case "fbsql result":
			case "mssql result":
			case "msql query":
			case "pgsql result":
			case "sybase-db result":
			case "sybase-ct result":
			case "mysql result":
				$db = current(explode(" ", get_resource_type($var)));
				$this->varIsDBResource($var, $db);
				break;
			case "gd":
				$this->varIsGDResource($var);
				break;
			case "xml":
				$this->varIsXmlResource($var);
				break;
			default:
				echo get_resource_type($var).$this->closeTDRow();
				break;
		}
		echo $this->closeTDRow()."</table>\n";
	}

	//if variable is a database resource type
	function varIsDBResource($var, $db = "mysql"){
		if($db == "pgsql"){
			$db = "pg";
		}
		if($db == "sybase-db" || $db == "sybase-ct"){
			$db = "sybase";
		}
		$arrFields = ["name", "type", "flags"];
		$numrows   = call_user_func($db."_num_rows", $var);
		$numfields = call_user_func($db."_num_fields", $var);
		$this->makeTableHeader("resource", $db." result", $numfields + 1);
		echo "<tr><td class=\"dBug_resourceKey\">&nbsp;</td>";
		for($i = 0; $i < $numfields; $i++){
			$field_header = "";
			for($j = 0; $j < count($arrFields); $j++){
				$db_func = $db."_field_".$arrFields[$j];
				if(function_exists($db_func)){
					$fheader = call_user_func($db_func, $var, $i)." ";
					if($j == 0){
						$field_name = $fheader;
					}else{
						$field_header .= $fheader;
					}
				}
			}
			$field[$i] = call_user_func($db."_fetch_field", $var, $i);
			echo "<td class=\"dBug_resourceKey\" title=\"".$field_header."\">".$field_name."</td>";
		}
		echo "</tr>";
		for($i = 0; $i < $numrows; $i++){
			$row = call_user_func($db."_fetch_array", $var, constant(strtoupper($db)."_ASSOC"));
			echo "<tr>\n";
			echo "<td class=\"dBug_resourceKey\">".($i + 1)."</td>";
			for($k = 0; $k < $numfields; $k++){
				$tempField = $field[$k]->name;
				$fieldrow  = $row[($field[$k]->name)];
				$fieldrow  = ($fieldrow == "") ? "[empty string]" : $fieldrow;
				$tdstyle   = ($fieldrow == '[empty string]') ? ' style="font-size:9px !important" ' : '';
				echo "<td {$tdstyle} >".$fieldrow."</td>\n";
			}
			echo "</tr>\n";
		}
		echo "</table>";
		if($numrows > 0){
			call_user_func($db."_data_seek", $var, 0);
		}
	}

	function varIsGDResource($var){
		$this->makeTableHeader("resource", "gd", 2);
		$this->makeTDHeader("resource", "Width");
		echo imagesx($var).$this->closeTDRow();
		$this->makeTDHeader("resource", "Height");
		echo imagesy($var).$this->closeTDRow();
		$this->makeTDHeader("resource", "Colors");
		echo imagecolorstotal($var).$this->closeTDRow();
		echo "</table>";
	}

	function varIsXml($var){
		$this->varIsXmlResource($var);
	}

	function varIsXmlResource($var){
		$xml_parser = xml_parser_create();
		/** @noinspection PhpUndefinedConstantInspection */
		xml_parser_set_option($xml_parser, XML_OPTION_CASE_FOLDING, 0);
		xml_set_element_handler($xml_parser, [&$this, "xmlStartElement"], [&$this, "xmlEndElement"]);
		xml_set_character_data_handler($xml_parser, [&$this, "xmlCharacterData"]);
		xml_set_default_handler($xml_parser, [&$this, "xmlDefaultHandler"]);

		$this->makeTableHeader("xml", "xml document", 2);
		$this->makeTDHeader("xml", "xmlRoot");

		//attempt to open xml file
		/** @noinspection PhpAssignmentInConditionInspection */
		$bFile = ( !($fp = @fopen($var, "r"))) ? false : true;

		//read xml file
		if($bFile){
			/** @noinspection PhpAssignmentInConditionInspection */
			while($data = str_replace("\n", "", fread($fp, 4096))){
				$this->xmlParse($xml_parser, $data, feof($fp));
			}
		}//if xml is not a file, attempt to read it as a string
		else{
			if( !is_string($var)){
				echo $this->error("xml").$this->closeTDRow()."</table>\n";
				return;
			}
			$data = $var;
			$this->xmlParse($xml_parser, $data, 1);
		}

		echo $this->closeTDRow()."</table>\n";
	}

	function xmlParse($xml_parser, $data, $bFinal){
		if( !xml_parse($xml_parser, $data, $bFinal)){
			die(sprintf("XML error: %s at line %d\n", xml_error_string(xml_get_error_code($xml_parser)), xml_get_current_line_number($xml_parser)));
		}
	}

	//xml: inititiated when a start tag is encountered
	function xmlStartElement($parser, $name, $attribs){
		$this->xmlAttrib[$this->xmlCount] = $attribs;
		$this->xmlName[$this->xmlCount]   = $name;
		$this->xmlSData[$this->xmlCount]  = '$this->makeTableHeader("xml","xml element",2);';
		$this->xmlSData[$this->xmlCount]  .= '$this->makeTDHeader("xml","xmlName");';
		$this->xmlSData[$this->xmlCount]  .= 'echo "<strong>'.$this->xmlName[$this->xmlCount].'</strong>".$this->closeTDRow();';
		$this->xmlSData[$this->xmlCount]  .= '$this->makeTDHeader("xml","xmlAttributes");';
		if(count($attribs) > 0){
			$this->xmlSData[$this->xmlCount] .= '$this->varIsArray($this->xmlAttrib['.$this->xmlCount.']);';
		}else{
			$this->xmlSData[$this->xmlCount] .= 'echo "&nbsp;";';
		}
		$this->xmlSData[$this->xmlCount] .= 'echo $this->closeTDRow();';
		$this->xmlCount++;
	}

	//xml: initiated when an end tag is encountered
	function xmlEndElement($parser, $name){
		for($i = 0; $i < $this->xmlCount; $i++){
			eval($this->xmlSData[$i]);
			$this->makeTDHeader("xml", "xmlText");
			echo ( !empty($this->xmlCData[$i])) ? $this->xmlCData[$i] : "&nbsp;";
			echo $this->closeTDRow();
			$this->makeTDHeader("xml", "xmlComment");
			echo ( !empty($this->xmlDData[$i])) ? $this->xmlDData[$i] : "&nbsp;";
			echo $this->closeTDRow();
			$this->makeTDHeader("xml", "xmlChildren");
			unset($this->xmlCData[$i], $this->xmlDData[$i]);
		}
		echo $this->closeTDRow();
		echo "</table>";
		$this->xmlCount = 0;
	}

	//xml: initiated when text between tags is encountered
	function xmlCharacterData($parser, $data){
		$count = $this->xmlCount - 1;
		if( !empty($this->xmlCData[$count])){
			$this->xmlCData[$count] .= $data;
		}else{
			$this->xmlCData[$count] = $data;
		}
	}

	//xml: initiated when a comment or other miscellaneous texts is encountered
	function xmlDefaultHandler($parser, $data){
		//strip '<!--' and '-->' off comments
		$data  = str_replace(["&lt;!--", "--&gt;"], "", htmlspecialchars($data));
		$count = $this->xmlCount - 1;
		if( !empty($this->xmlDData[$count])){
			$this->xmlDData[$count] .= $data;
		}else{
			$this->xmlDData[$count] = $data;
		}
	}

	function initJSandCSS(){
		echo dBugInitJSandCSS();// so we can use it, if needed, like if its getting hidden in an ob or sth
	}
}//class ssssssssssssssssssssssssssssssssss

function d_var_export($var){
	$out = wpre(var_export($var, true));
	// $out = str_replace('=>'.' \n ', ' => array', $out);
	// dont work. $out = preg_replace('/=>[\n\r]array/', '=> array', $out);
	return $out;
}

// unused, works
function varexport($expression, $return = true){
	$export = var_export($expression, true);
	$export = preg_replace("/^([ ]*)(.*)/m", '$1$1$2', $export);
	$array  = preg_split("/\r\n|\n|\r/", $export);
	$array  = preg_replace(["/\s*array\s\($/", "/\)(,)?$/", "/\s=>\s$/"], [null, ']$1', ' => ['], $array);
	$export = join(PHP_EOL, array_filter(["["] + $array));
	if((bool)$return){
		return $export;
	}else{
		echo $export;
	}
}


function dhtml($var, $note = false, $args = []){
	$args['location'] = ddumpwhereCalled();
	d(wpre(htmlentities($var)), $note, $args);
}

function ddump($var, $return = false){
	$result = var_export($var, true);
	$loc    = ddumpwhereCalled();
	$out    = "\n<pre>Dump: $loc\n$result</pre>";
	if($return){
		return $out;
	}
	echo $out;
}

function ddumpwhereCalledFilenameSlug($level = 1){
	$trace = debug_backtrace();//	d($trace[$level], '$trace[$level]');
	$file  = $trace[$level]['file'];
	$file  = ($file) ? : 'unk - ddumpwhereCalledPathOnly()';
	$file  = endOfPath($file, 3);
	$file  = makeValidArrayKey($file);
	$file  = str_replace('.php', '_php', $file);

	return $file;
}



function ddumpwhereCalled($level = 1, $short = true){
	$trace = debug_backtrace();
	//pre($trace);
	if(isset($trace[$level]['file'])){
		$file = $trace[$level]['file'];
	}else{
		d($trace);
		d($level);
		$file = 'unk - ddumpwhereCalled()';
		return $file;
	}

	if(is_bool($short) and $short){
		$file = endOfPath($file, 3);
	}elseif(is_numeric($short)){
		$file = endOfPath($file, $short);
	}
	//quickd($trace, '$trace');
	//quickd($level, '$level');
	//die('ded '.__FILE__.' '.__LINE__);
	//$trace[$level]['object'] = ($trace[$level]['object'])?$trace[$level]['object']:'';
	$trace[$level]['object'] = $trace[$level]['object'] ?? '';

	$line   = $trace[$level]['line'];
	$object = $trace[$level]['object'];
	$obj    = (is_object($object)) ? get_class($object) : '';
	//echo br($trace,'trace');
	//echo br($file,'file');
	//echo br($short,'short');
	if($short){
		$r = $file." :ln:$line";
	}else{
		$r = wspan($file).":ln:$line";//dunno why thats in a span.
	}
	//echo br($r,'r');
	return $r;
	// return "$obj:$file :ln:$line";
}

function dReturn($array, $note = false, $args = []){
	if(is_array($note)){
		$args = $note;
		$note = false;
	}
	$args = wp_parse_args($args, ['alwaysJS' => true, 'public' => true, 'location' => ddumpwhereCalled()]);

	ob_start();
	d($array, $note, $args);
	return ob_get_clean();
}

//this is in _functions.inc.php
//function is_dev(){
//	if(definedandtrue('dugdebug_override'))return true;
//	return definedandtrue('dugdebug');//we dont need to check for overrides, cuz we already do, when we
// define dugdebug in global class ---NOOOOOOO we dont do this anymore.  we do it in wp-config or bootstrap
//}
//shouldnt we be using thee dugdebugfunc() ?????
function dugdebugfunc($override = false){
	$d   = false;
	$dev = is_dev();
	//	quickd($GLOBALS['dbug_disable'], '$GLOBALS[dbug_disable]', [__FILE__, __LINE__, __FUNCTION__]);
	//	quickd($GLOBALS['dbug_public'], '$GLOBALS[dbug_public]', [__FILE__, __LINE__, __FUNCTION__]);
	//	quickd($dev, '$dev is_dev()', [__FILE__, __LINE__, __FUNCTION__]);
	//	quickd($override, '$override', [__FILE__, __LINE__, __FUNCTION__]);
	//quickd($GLOBALS['dbug_disable'], '$GLOBALS[dbug_disable] -- ', [__FILE__, __LINE__, __FUNCTION__]);
	//quickd(isset($GLOBALS['dbug_disable']), 'isset($GLOBALS[dbug_disable]) -- ', [__FILE__, __LINE__, __FUNCTION__]);
	if(isset($GLOBALS['dbug_disable']) and $GLOBALS['dbug_disable']){
		//quickd(isset($GLOBALS['dbug_disable']) and !$GLOBALS['dbug_disable'], 'isset($GLOBALS[dbug_disable]) and !$GLOBALS[dbug_disable]');
		return false;
	}

	if(isset($GLOBALS['dbug_public']) and $GLOBALS['dbug_public']){
		//quickd(isset($GLOBALS['dbug_public']) and $GLOBALS['dbug_public'], 'isset($GLOBALS[dbug_public]) and $GLOBALS[dbug_public]');
		return true;
	}

	if($dev or $override){// override
		//quickd($dev or $override, '$dev or $override');
		$d = true;
	}
	$filtered = apply_filters('dugdebugfunc', $d);
	//quickd($filtered, '$filtered -- ', [__FILE__, __LINE__, __FUNCTION__]);
	if($filtered != $d){
		//		quickd($d, '$d pre filter', [__FILE__, __LINE__, __FUNCTION__]);
		//quickd($filtered, '$filtered changed', [__FILE__, __LINE__, __FUNCTION__]);
		$d = $filtered;
	}
	//quickd($d, '$ddd', [__FILE__, __LINE__, __FUNCTION__]);
	return $d;
}

function returnDbug($array, $note = false, $fileline = false, $override = false, $alwaysJS = true){
	return comment('deprecated returnDbug use dReturn').dReturn($array, $note, $fileline, $override, $alwaysJS);
}

//function dfoot($array,$note = false, $fileline = true, $dbugfuncparam = false, $alwaysJS = true){ this dont work but it would be cool.
// prolly would need to do the dreturn in the dfoot() func nad then send it to the action
//	add_action( 'admin_footer',  		function(){
//			echo returnDbug($array,$note , $fileline , $dbugfuncparam , $alwaysJS );
//	}
//	, 1, 0 );
//	add_action( 'wp_footer',  		function(){
//			echo returnDbug($array,$note , $fileline , $dbugfuncparam , $alwaysJS );
//	}
//	, 1, 0 );
//
//}

// so i can use it, if needed, like if its getting hidden in an ob or sth
function dBugInitJSandCSS(){
ob_start();
?><!-- initJSandCSS() -->
<script>
	function dBug_toggleRow(source) {
		var target = (document.all) ? source.parentElement.cells[1] : source.parentNode.lastChild;
		dBug_toggleTarget(target, dBug_toggleSource(source));
	}

	function dBug_toggleSource(source) {
		if (source.style.fontStyle == 'italic') {
			source.style.fontStyle = 'normal';
			source.title = 'click to collapse';
			return 'open';
		} else {
			source.style.fontStyle = 'italic';
			source.title = 'click to expand';
			return 'closed';
		}
	}

	function dBug_toggleTarget(target, switchToState) {
		target.style.display = (switchToState == 'open') ? '' : 'none';
	}

	function dBug_toggleTable(source) {
		var switchToState = dBug_toggleSource(source);
		if (document.all) {
			var table = source.parentElement.parentElement;
			for (var i = 1; i < table.rows.length; i++) {
				target = table.rows[i];
				dBug_toggleTarget(target, switchToState);
			}
		} else {
			var table = source.parentNode.parentNode;
			for (var i = 1; i < table.childNodes.length; i++) {
				target = table.childNodes[i];
				if (target.style) {
					dBug_toggleTarget(target, switchToState);
				}
			}
		}
	}
</script>

<style id="dbug_style">
	div.dbugdiv {
		max-width: 100% !important;
	}

	div.dbugdiv * {
		white-space: wrap; /* this keeps it from being too wide */
	}

	/* these2  are new */
	table.dBug {
		border-spacing: 0 !important;
	}

	table.dBug td {
		border-bottom: 1px solid #ccc;
	}

	table.makeTableHeader td {
		/*font-size: 10px !important;*/
		font-size: 12px !important;
		padding: 2px !important;
	}

	table.dBug_array, table.dBug_object, table.dBug_resource, table.dBug_resourceC, table.dBug_xml {
		font-family: "Consolas", monospace !important;
		position: relative;
		z-index: 999999999;
		color: #222 !important;
		margin: 0 !important;
	}

	table.dBug_array *, table.dBug_object *, table.dBug_resource *, table.dBug_resourceC *, table.dBug_xml * {
		font-family: "Consolas", monospace !important;
		color: #222 !important;
		text-align: left !important;
		width: auto !important;
	}

	table.dBug_array a:visited, table.dBug_array a:active,
	table.dBug_object a:visited, table.dBug_object a:active {
		color: #310b56 !important;
	}

	table.dBug_array a, table.dBug_object a {
		/*	text-decoration: none !important;*/
		text-decoration-color: #d2acf7;
	}

	.dBug_arrayHeader, .dBug_objectHeader, .dBug_resourceHeader, .dBug_resourceCHeader, .dBug_xmlHeader {
		font-weight: bold;
		color: #FFFFFF !important;
		cursor: pointer;
	}

	.dBug_arrayHeader span, .dBug_objectHeader span, .dBug_resourceHeader span, .dBug_resourceCHeader span, .dBug_xmlHeader span {
		font-weight: bold;
		color: #777 !important;
		cursor: pointer;
	}

	.dBug_arrayKey, .dBug_objectKey, .dBug_xmlKey {
		cursor: pointer;
	}

	table.dBug_array {
		background-color: #006600 !important;
	}

	table.dBug_array td, table.dBug_object td, table.dBug_resourceC td, table.dBug_resource td, table.dBug_xml td {
		background-color: #FFFFFF !important;
	}

	table.dBug_array td.dBug_arrayHeader {
		background-color: #A0E19F !important;
	}

	table.dBug_array td.dBug_arrayKey {
		background-color: #CCFFCC !important;
	}

	table.dBug_object {
		background-color: #0000CC !important;
	}


	table.dBug_object td.dBug_objectHeader {
		background-color: #C5C5E8 !important;
	}

	table.dBug_object td.dBug_objectKey {
		background-color: #CCDDFF !important;
	}

	table.dBug_resourceC {
		background-color: #884488 !important;
	}


	table.dBug_resourceC td.dBug_resourceCHeader {
		background-color: #AA66AA !important;
	}

	table.dBug_resourceC td.dBug_resourceCKey {
		background-color: #FFDDFF !important;
	}

	table.dBug_resource {
		background-color: #884488 !important;
	}


	table.dBug_resource td.dBug_resourceHeader {
		background-color: #AA66AA !important;
	}

	table.dBug_resource td.dBug_resourceKey {
		background-color: #FFDDFF !important;
	}

	table.dBug_xml {
		background-color: #888888 !important;
	}

	table.dBug_xml td.dBug_xmlHeader {
		background-color: #AAAAAA !important;
	}

	table.dBug_xml td.dBug_xmlKey {
		background-color: #DDDDDD !important;
	}

	table .racount {
		color: #999 !important;
		font-weight: lighter !important;
	}

	.dug {
		direction: inherit;
	}

</style>
<?php

$ob = ob_get_clean();
//echo '<head class="urg">'.removeDupeWhiteSpaces(stripNewLinesPreg($ob)).'</head><!-- urgh -->';// this head is here to make gmail do my styles.
return '<div class="ob_get_clean debug_php cf clearfix">'.removeDupeWhiteSpaces(stripNewLinesPreg($ob)).'</div>';
//echo hr().hr().hr();
}

function is_devdBugInitJSandCSS(){
	if(is_dev()){
		return dBugInitJSandCSS();
	}
}

function is_devdBugInitJSandCSSecho(){
	if(is_dev()){
		echo dBugInitJSandCSS();
	}
}

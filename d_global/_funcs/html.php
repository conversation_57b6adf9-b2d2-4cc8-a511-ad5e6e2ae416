<?php

function wtitle($title){
	return "<title>$title</title>";
}

function whtmlTag($name, $content = false, $attribs = ''){
	$attribs = formatAttribs($attribs);
	if($content === false){
		$tag = "<{$name} {$attribs} />";
	}else{
		$tag = "<{$name} {$attribs} >{$content}</{$name} >";
	}
	return $tag;
}

function htmlTag($name, $attribs = ''){//for single sided tags like img
	return whtmlTag($name, false, $attribs);
}

// xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

function wbold($content, $attribs = ''){
	return whtmlTag('strong', $content, $attribs);
}

function wstrong($content, $attribs = ''){
	return wbold($content, $attribs);
}

//tables xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
function table(){
	extract(closeOrAttribs(func_get_args()));
	$moreAtts = ($close == '/') ? '' : ' cellpadding="0" cellspacing="0" ';
	return nlt()."<{$close}table {$attribs} $moreAtts >";
}

function tr(){
	extract(closeOrAttribs(func_get_args()));
	return nlt()."<{$close}tr {$attribs}>";
}

function td(){
	extract(closeOrAttribs(func_get_args()));
	return nlt()."<{$close}td {$attribs}>";
}

function wtable($content, $attribs = ''){
	$attribs = formatAttribs($attribs);// if string, just returns it.
	return nl()."<table {$attribs}><tbody>{$content}".nl()."</tbody></table>";
}

function wtr($content, $attribs = ''){
	$attribs = formatAttribs($attribs);// if string, just returns it.
	return nlt()."<tr {$attribs}>{$content}".nlt()."</tr>";
}

function wtd($content, $attribs = ''){
	$attribs = formatAttribs($attribs);// if string, just returns it.
	/*$atts    = ['id'       => 'someid',
				'class'    => 'someclass',
				'style'    => ['background-color' => '#fff !important', 'color' => '#000'], // could be  array or string
				'style'    => 'background-color: #fff !important; color: #000', // could be  array or string
				'data-foo' => 'bar',];*/
	return nlt()."<td {$attribs}>{$content}</td>";
}

//<editor-fold desc="-- temp fold -- ">

function wth($content, $attribs = ''){
	return nlt()."<th {$attribs}>{$content}</th>";
}

function trFromArray($array, $type = 'td'){
	$tds = '';
	foreach($array as $item){
		if($type == 'th'){
			$tds .= wth($item);
		}else{
			$tds .= wtd($item);
		}
	}
	return wtr($tds);
}

// this is the best table now 7-22-22
/*$trows[]= array('d_rowAttribs' => 'class="thisRowclass" id="thsi rowid" '
				 ,'col1' => array('r1.1','class="sth"')
				 ,'col2' => 'r1.2'
				 );
$trows[]= array(
				 'col1' => array('r2.1','class="sth"')
				,'col2' => array('r2.2','class="sth2"')
				);
d($trows);
you dont need to set up the header row, with this one.
echo tableFromRows2($trows, array( 'tableattribs' => ' id="myTableID" class="zebra left_align" ', 'headerlast' => true, 'headerEveryRows' => 8)   );
 <table id="myTableID" class="zebra left_align"><tbody>
	<tr>
		<th>col1</th>
		<th>col2</th>
	</tr>
	<tr class="thisRowclass" id="thsi rowid">
		<td class="sth">r1.1</td>
		<td>r1.2</td>
	</tr>
	<tr>
		<td class="sth">r2.1</td>
		<td class="sth2">r2.2</td>
	</tr>
	<tr>
		<th>col1</th>
		<th>col2</th>
	</tr>
</tbody></table>
you dont need to set up the header row, with this one. */
function tableFromRows2($rows, $args = ''){
	if(empty($rows)){
		return comment('no rows for tableFromRows2');
	}
	if(is_string($args)){
		$args = ['tableattribs' => $args];
	}//dont really need this, just pass in a string attribs
	extract(wp_parse_args($args, ['tableattribs'      => '',
								  'headerAttribs'     => '',
								  'headerfirst'       => true,
								  'headerfirst'       => true,
								  'headerlast'        => false,
								  'headerEveryRows'   => false,
								  'columnToHeaderMap' => []// this is an array that maps column names to header names so you dont have to use lame_col_names.
								  // but you have to have all the keys that you want to keep. so you might have to do 'some_col_name' => 'some col name'
	]));
	if($columnToHeaderMap){
		$rows = arrayOfArraysFilterKeepKeys($rows, array_keys($columnToHeaderMap));
	}
	// normalize the rows
	//d($rows, '$rows');
	foreach($rows as $key => $row){
		//d($row, '$row	');//d($key, '$key');//these are just numbers.  so far.  maybe they could be a class name or sth.
		foreach($row as $colkey => $col){
			//ehr();
			//d($colkey, '$colkey');
			//d($col, '$col');
			if( !is_array($col)){//if its not, make it an array. otherwise, will already be [0]=data, [1]=cell attribs
				if($colkey == 'd_rowAttribs'){
					$rows[$key][$colkey] = $col;
				}else{
					$rows[$key][$colkey] = [0 => $col, 1 => ''];
				}
			}
		}
	}
	//dCollapsed($rows, '$rows');
	$rowcount = count($rows);
	$rowsi    = 1;
	foreach($rows as $key => $row){
		if($rowsi == 1){
			foreach($row as $colkey => $col){
				//				d($colkey, '$colkey	');
				//				d($col, '$col');
				//				d($row, '$row ');
				//				d($key, '$key');
				if($colkey != 'd_rowAttribs'){//this is how you'd send atts for the row.
					$headerTitle = ($columnToHeaderMap) ? $columnToHeaderMap[$colkey] : $colkey;
					$headerCols  .= wth($headerTitle, $col[1]);
				}else{
					$headerAttribs = $col;
				}
			}
			//d($headerAttribs);
			$header = wtr($headerCols, $headerAttribs);
			//dhtml($header, '$header');
		}//  make the header row

		if(($rowsi == 1 and $headerfirst) or ($headerEveryRows and divBy($rowsi, $headerEveryRows))){
			$tableRows[] = $header;
		}

		$tds = '';
		//dCollapsed($row, '$row');
		foreach($row as $colkey => $col){//why was this here? if(!is_numeric($k))continue;
			//d($colkey, '$colkey');
			//d($col, '$col');
			if($colkey == 'd_rowAttribs'){
				$rowAttribs = $row[$colkey];//it will have been made into a ra.  probly the other elements wont ever be used.
				continue;                   //dont put the attribs into a td
			}else{
				$tds .= wtd($col[0], $col[1]);
			}
			//dhtml($tds, '$tds');
		}

		$r = wtr($tds, $rowAttribs);
		//dhtml($r, 'r');
		$tableRows[] = $r;

		if($headerlast and $rowsi == $rowcount){
			$tableRows[] = $header;
		}
		$rowsi++;
	}//foreach rows

	$tableRows = implode(nl(), $tableRows);
	//dhtml($tableRows);
	// this is annoying  $tit = (is_dev()) ? ' title="'.ddumpwhereCalled().' -is_dev()" ' : '';
	$out = wtable($tableRows, $tableattribs.$tit);
	//dhtml($out, '$out');
	return $out;
}



function closeOrAttribs($args){
	$close = $attribs = $params = $comment = '';
	switch(count($args)){
		case 0:
			break;
		case 1:// one arg: either close (0) or params
			$attribs = $args[0];//assume for now, its
			if($args[0] === 0){
				$close   = '/';
				$attribs = '';
			}
			break;
		case 2: // has to be a close and a comment
			$close   = '/';
			$comment = $args[1];
			break;
	}
	return compact('close', 'params', 'attribs', 'comment');
}

// xxxxxxxxxx label used in product sidebar search but not much
function wlabel($for, $content){
	return nlt().'<label for="'.$for.'">'.$content.'</label>';
}

// headers xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
function wHnum($num, $content, $attribs = ''){
	//d($attribs, '$attribs');
	$attribs = formatAttribs($attribs);
	//d($attribs, '$attribs');
	return nlt()."<h{$num} {$attribs}>{$content}</h{$num}>";
}

function wh1($content, $attribs = ''){
	return wHnum('1', $content, $attribs);
}

function wh2($content, $attribs = ''){
	return wHnum('2', $content, $attribs);
}

function wh3($content, $attribs = ''){
	return wHnum('3', $content, $attribs);
}

function wh4($content, $attribs = ''){
	return wHnum('4', $content, $attribs);
}

function wh5($content, $attribs = ''){
	return wHnum('5', $content, $attribs);
}

function wh6($content, $attribs = ''){
	return wHnum('6', $content, $attribs);
}

// lists xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
function htmllist($type, $attribs = ''){
	if($attribs === 0){
		$slash = "/";
		$atts  = '';
	}else{
		$slash = '';
		$atts  = $attribs;
	}
	return nl()."<{$slash}{$type} {$atts}>".nl();
}

function arraytolist($array, $args = []){
	extract(wp_parse_args($args, ['type'     => 'ol',
								  'listAtts' => null, // string or we'll formatAttribs($attribs)
	]));
	foreach((array)$array as $li){
		$content = is_array($li) ? $li[0] : $li;
		$attribs = is_array($li) ? $li[1] : '';
		$lis     .= wli($content, $attribs);
	}
	$atts = (is_array($listAtts)) ? formatAttribs($listAtts) : $listAtts;
	return nl()."<{$type} {$atts}>".$lis.nl()."</{$type}>".nl();
}

// pretty sure this is unused.. //function wlist($type = 'ol', $listattribs = '', $array = []){ // /*	$args = ['type'     => $type, 'listAtts' => $listattribs]; return arraytolist($array, $args); }*/

function ol($attribs = ''){
	return htmllist('ol', $attribs);
}

function ul($attribs = ''){
	return htmllist('ul', $attribs);
}

function wul($content, $attribs = ''){
	$attribs = formatAttribs($attribs);
	return nl()."<ul {$attribs}>{$content}".nl()."</ul>".nl();
}

function wli($content, $attribs = ''){
	$attribs = formatAttribs($attribs);
	return nlt()."<li {$attribs}>{$content}</li>";
}

function wrap_i($content, $attribs = ''){
	$attribs = formatAttribs($attribs);
	return nlt()."<i {$attribs}>{$content}</i>";
}

function li(){
	extract(closeOrAttribs(func_get_args()));
	if($close == '/' and !empty($comment)){
		$comment = comment($comment, false);
	}
	return nlt()."<{$close}li {$attribs}>".$comment;
}


// divs xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
//end like this echo div(0, 'end wrapper'); gives you a comment
function div(){
	$loc = ddumpwhereCalled();
	extract(closeOrAttribs(func_get_args()));//closeOrAttribs  case 2: // has to be a close and a comment div(0, 'end wrapper')
	//d($attribs, '$attribs');
	$attribs = formatAttribs($attribs);

	//quickd(get_defined_vars(), 'get_defined_vars()', [__FILE__, __LINE__, __FUNCTION__]);
	if($close == '/' and !empty($comment)){
		$comment = comment($comment, ['loc' => $loc]);
	}
	return nlt()."<{$close}div {$attribs}>".$comment;
}

function wdiv($content = "&nbsp;", $attribs = '', $comment = ''){
	$attribs = formatAttribs($attribs);
	if( !empty($comment)){
		$comment = comment($comment, false);
	}
	if(is_array($content)){
		$content = implode(PHP_EOL, $content);
	}
	return nlt()."<div {$attribs}>{$content}</div>".$comment.nl();
}

function wspan($content, $attribs = '', $comment = ''){
	$attribs = formatAttribs($attribs);
	if( !empty($comment)){
		$comment = comment($comment, false);
	}

	return nlt()."<span {$attribs}>{$content}</span>".$comment;
}

function wrapColor($content = "&nbsp;", $args = []){
	$red     = '#e18d8d';
	$green   = '#90ee90';// 'LightGreen';
	$yellow  = '#fcffa4';
	$comment = basename(strip_tags(ddumpwhereCalled()));
	//d($content,'content');
	//d($args, '$args');
	if(is_bool($args) and !is_array($args)){
		//dbr('yeahhh', 'yeahhh');
		$orig = $args;
		$args = ['testbool' => $orig];
	}elseif( !is_array($args)){
		$args = ['testbool' => $args];
	}
	$bool    = ($args['testbool']) ? $args['testbool'] : stringToBool($content);
	$color   = ($bool) ? $green : $red;
	$content = (is_bool($content)) ? boolToString($content) : $content;
	// no $content = ( is_numeric($content) and $content) ? 'true': 'false';
	return wspan($content, ' style="background:'.$color.'" title="'.$comment.'" ');
}

// paragraphs xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
function wpara($content = "&nbsp;", $attribs = ''){
	$attribs = formatAttribs($attribs);
	return nlt()."<p {$attribs}>{$content}</p>".nl();
}

function para(){
	extract(closeOrAttribs(func_get_args()));
	return nlt()."<{$close}p {$attribs}>".nl();
}

function wFooter($content = "&nbsp;", $attribs = ''){
	$attribs = formatAttribs($attribs);
	return nlt()."<footer {$attribs}>{$content}</footer>".nl();
}

function footerTag(){
	extract(closeOrAttribs(func_get_args()));
	return nlt()."<{$close}footer {$attribs}>".nl();
}

function hr($before = '', $attribs = 'style="clear:both;margin-left: 0;"'){
	return $before.nl()."<HR {$attribs}>	";
}

function hrshort($before = '', $attribs = 'style="clear:both;margin-left: 0;width:25vw;opacity: .3;"'){
	return $before.nl()."<HR {$attribs}>	";
}

function hrthick($before = '', $attribs = 'style="clear:both;margin-left: 0;height: 2%;background-color: black;"'){
	return $before.nl()."<HR {$attribs}>	";
}

//
//
//ddumpwhereCalled()

function ehrthick($after = '', $attribs = 'style="clear:both; margin-left: 0;height: 1vw !important; background-color: black !important;"'){
	$comment = basename(strip_tags(ddumpwhereCalled()));
	//$after   = dbr($after, '', ['return' => true, 'location' => ddumpwhereCalled()]);
	$atss = ' title="'.$comment.'" '.$attribs.'  ';
	echo hrthick('', $atss).nl().$after;
}

function ehrshort($after = '', $attribs = 'style="clear:both;margin-left: 0;width:25vw;opacity: .3;"'){
	$comment = basename(strip_tags(ddumpwhereCalled()));
	$atss    = ' title="'.$comment.'" '.$attribs.'  ';
	//$after   = dbr($after, '', ['return' => true, 'location' => ddumpwhereCalled()]);
	echo hrshort('', $atss);
}

function ehrthin($after = '', $attribs = 'style="clear:both;margin-left: 0;opacity: .3;"'){
	$comment = basename(strip_tags(ddumpwhereCalled()));
	$atss    = ' title="'.$comment.'" '.$attribs.'  ';
	//$after   = dbr($after, '', ['return' => true, 'location' => ddumpwhereCalled()]);
	echo hrshort('', $atss);
}

function ehr($numberOfHRsOrStringForAfter = 1){// num can be number or hrs or string for after.
	$comment = basename(strip_tags(ddumpwhereCalled()));
	$atss    = ' title="'.$comment.'" ';
	if(is_numeric($numberOfHRsOrStringForAfter)){
		for($i = 1; $i <= $numberOfHRsOrStringForAfter; $i++){
			echo nl()."<HR {$atss} style='clear:both !important;'>	";
		}
	}else{
		echo nl()."<HR {$atss} style='clear:both !important;'> {$numberOfHRsOrStringForAfter}	";
	}
}

function ebr($before = '', $atts = 'clear="all"'){
	rbr($before, $atts, true);
}

function br($before = '', $atts = 'clear="all"'){
	return rbr($before, $atts);
}

function rbr($before = '', $atts = 'clear="all"', $echo = false){
	$br = $before."<br $atts />";
	if($echo){
		echo nl().$br;
	}
	return nl().$br;
}

function wpre($wrapped = '', $attribs = ''){
	$attribs = formatAttribs($attribs);

	if( !is_string($wrapped) and !is_numeric($wrapped)){
		return '<pre style="background:#fff !important;color:#000 !important;">'.var_export($wrapped, true).'</pre>';
	}else{
		return "<pre $attribs style='margin:0;' >$wrapped</pre>";
	}
}

function pre(){
	extract(closeOrAttribs(func_get_args()));
	return nlt()."<{$close}pre {$attribs}>".nl();
}

function atag($url, $txt = '', $attribs = '', $target = '_self'){
	return areturn($url, $txt, $attribs, $target);
}

function atag_blank($url, $txt = '', $attribs = '', $target = '_blank'){
	return areturn($url, $txt, $attribs, $target);
}

function atagblank($url, $txt = '', $attribs = '', $target = '_blank'){//alians
	return atag_blank($url, $txt, $attribs, $target);
}

function areturn($url, $txt = '', $attribs = '', $target = '_self'){
	$txt       = ($txt == '') ? $url : $txt;
	$attribs   = formatAttribs($attribs);
	$targetatt = '';
	if($target != '_self'){
		$targetatt = 'target="'.$target.'"';
	}//dont bother with target unless it's not _self

	if($url){
		$url = 'href="'.$url.'"';
	}else{
		$url = '';
	}// might not have an href??  i gues...
	return nl()."<a $url $targetatt $attribs >$txt</a>".nl();
}

// \260E is a telephone icon.  could add it in.  http://webdesign.tutsplus.com/articles/quick-tip-make-telephone-numbers-do-something--webdesign-9271
//  a[href^="tel:"]:before {
//	content: "\260E"; display: block; margin-right: 0.5em;
function phonelink($args = null, $text = null){
	if( !is_array($args) and $args){
		$args = ['num' => $args, 'text' => $text];
	}

	$args = wp_parse_args($args, ['num'  => getThemeOption('contactphone'),
								  'text' => $text]);
	extract($args);
	if( !$text){
		$text = $num;
	}

	if( !isMobile()){
		return $num;
	}

	return '<a href="tel:'.preg_replace('/[^0-9]/', ' ', $num).'">'.$text.'</a>';
}

function googleMeetLink($url, $linktxt = null, $args = []){
	$args = wp_parse_args($args, ['atts' => ['style'   => '',        //
											 'classes' => [],        //
											 'target'  => '_blank'], //
								  'icon' => urlto_incimg.'googlemeet.png',// or dashicons-location is a pin, -alt is a trifold map with  a pin
	]);
	$atts = formatAttribs($args['atts']);

	$linktxt = $linktxt ?? $search;
	$url     = esc_url($url);
	return "<a href='{$url}' {$atts} ><img style='width:20px; margin-right:10px;' src=' {$args['icon']}' >{$linktxt}</a>".PHP_EOL;
}

function img($src, $attribs = ''){
	$attribs = formatAttribs($attribs);
	return wnl('<img src="'.$src.'" '.$attribs.' />');
}

function preloadimg($raOfImgsUrls, $echo = true, $attribs = ' class="preloadimg" style="position:absolute; top: -9999rem;  left: -9999rem; width: 1px; height: 1px;  opacity: 0.01;"'){
	////echo '<link rel="preconnect" href="'.$urlpath.$over.'">';
	/// preconnect dont seem to work, but this does:
	foreach((array)$raOfImgsUrls as $urltoimg){
		$img = img($urltoimg, $attribs);
		if($echo){
			echo $img.PHP_EOL;
			continue;
		}
		$imgs[] = $img;
	}
	return implode(' ', (array)$imgs);
}

function buttonWithLink($text, $link = null, $attribs = ''){
	//function img($src,$attribs = ''){
	$attribs = formatAttribs($attribs);
	if(haystackcontainsneedle($attribs, '_blank')){
		$target = "_blank";
	}else{
		$target = "_top";
	}
	$link = $link ?? geturl();
	$link = "window.open('".$link."','".$target."' ); ";

	//$link = "window.location.href='".$link."'";
	return wnl('<input TYPE="button" VALUE="'.$text.'" onclick="'.$link.'" '.$attribs.' >');
}

//function buttonNoLink   // see form fields.php

function returncomment($str){
	return comment($str, false);
}

function comment($str = '', $echo = false, $public_comment = false, $ddumpwhereCalled = true){
	if(is_array($echo)){
		extract(wp_parse_args($echo, ['echo'             => false,
									  'public_comment'   => false,
									  'ddumpwhereCalled' => true,
									  'loc'              => '']));
	}
	if( !is_dev() and !$public_comment){
		return;
	}

	if( !$loc and $ddumpwhereCalled){
		$loc = ddumpwhereCalled(1, 1);
	}

	$out = nl()."<!-- {$str}  --$loc --> ".nl();
	if( !$echo){
		return $out;
	}
	echo $out;
}

//</editor-fold> -- temp fold --
function formatAttribs($attribs){// if string, just returns it.
	if(is_array($attribs)){
		foreach($attribs as $k => $v){
			if($k == 'style' or $k == 'styles'){//inline styles
				$k = 'style';
				$v = (is_array($v)) ? formatStyles($v) : $v;
			}
			if($k == 'class' or $k == 'classes'){//inline styles
				$k = 'class';
				$v = (is_array($v)) ? implode(' ', $v) : $v;
			}

			if(istartsWith($k, 'data-')){// might be json with double quotes
				// but beware fucking cocksucker ff dev tools turns your single quotes to double.
				// look in view source and you'll see.
				$atts[] = "{$k}='{$v}' ";
			}else{
				$atts[] = $k.'="'.$v.'" ';
			}
		}
		$nl      = (count($atts) > 2) ? PHP_EOL : '';//just dont put a newline if there are only 2 attribs.
		$attribs = implode(' '.$nl, $atts);
	}
	return $attribs;
}

function formatStyles($styles){//must be an array
	foreach($styles as $k => $v){
		$thestyles[] = $k.': '.$v.'; ';
	}
	$r = implode(' ', $thestyles);
	return $r;
}

//$str['table.zebra'][] = 'max-width:1200px';
//$str['table.zebra'][] = 'color:pink';
function wstyle($str, $id = ''){
	$id = ($id) ? ' id="'.$id.'" ' : '';

	if(is_string($str)){
		return "<style {$id} > {$str} </style>";
	}

	//must be ra of $selector => $styles
	foreach($str as $selector => $styles){
		$out = "<style {$id} >".nl();
		$out .= $selector.'{'.nl();
		foreach($styles as $style){
			$style = trim($style, ';').';';
			$out   .= $style.nl();
		}
		$out .= '}'.nl().'</style>'.nl();
	}
	return $out;
}

function wscript($str, $atts = ''){
	return '<script '.$atts.' >'.$str.'</script>';
}

function echoJSfile($src, $atts = []){
	echo JSfile($src, $atts);
}

function JSfile($src, $atts = []){
	$atts['src'] = $src;
	$atts        = formatAttribs($atts);
	return "<script {$atts} ></script>".PHP_EOL;
}

// <link rel='stylesheet' id='hamburger.css-css' href='http://localhost/wp-content/plugins/wp-responsive-menu/assets/css/wpr-hamburger.css' type='text/css' media='all' />
function CSSfile($href, $atts = []){
	$atts         = wp_parse_args($args, ['rel'   => 'stylesheet',
										  'type'  => 'text/css',
										  'media' => 'all',]);
	$atts['href'] = $href;
	$atts         = formatAttribs($atts);
	return "<link {$atts} >".PHP_EOL;
}


function wscript_JQ_docready($str, $atts = ''){
	$atts = formatAttribs($atts);
	return '<script '.$atts.' >'.nlt('jQuery(document).ready(function($){').nlt($str).nlt('});//jQuery(document).ready').'</script>';
}

// newlines and tabs
function wnl($str){
	return nl().$str.nl();
}

function returntab($str = ""){
	return "\t".$str;
}//  func
function nlt($str = ''){
	return nl().returntab($str);
}

function nl($str = ""){
	return PHP_EOL.$str;
}

function enl($str = ""){
	echo nl($str);
}//  func

function wrapEachError($errors, $class = ''){//$class = 'error_msg'
	/*foreach((array)$errors as $e){
		$ret .= wdiv($e, 'class="'.$class.'"');
	}*/
	foreach((array)$errors as $e){
		$lis .= wli($e, 'class="'.$class.'"');
	}
	$ret = wul($lis, $attribs = '');
	return $ret;
}

function wError($error, $wpara = true, $class = 'error_msg'){
	if($wpara){
		return wpara($error, 'class="'.$class.'"');
	}
	return wspan($error, 'class="'.$class.'"');
}

function wrapEachNotice($notices, $class = 'notice_msg'){
	foreach((array)$notices as $n){
		$ret .= wdiv($n, 'class="'.$class.'"');
	}
	return $ret;
}

function doctypeHTMLtag($moreatts = ''){
	$atts['lang'] = echoToVar('language_attributes');//wordpress func.  Builds up a set of <HTML> attributes containing the text direction and language information for the page.
	//$atts['more'] = apply_filters('doctypeHTMLtag_more', ''); not used yet
	$atts['more'] = $moreatts;
	$atts         = implode(' ', $atts);

	$out = '<!DOCTYPE html>
	<html class="no-js" '.$atts.' >
	';
	$out = trim($out);
	$out = stripTabs($out);

	return trim($out).nl();
}

function boringMetaTags(){
	//		<!-- not sure if this shit is needed anymore.
	//		<meta name="HandheldFriendly" content="True">
	//		<meta name="MobileOptimized" content="320">
	//		<meta name="viewport" content="width=device-width, initial-scale=1"/>
	//		<meta name="MobileOptimized" content="width"> -->
	//	<meta name="viewport" content="width=device-width, minimum-scale=1, initial-scale=1"/>
	//// <meta name="viewport" content="minimum-scale=1, initial-scale=1"/>
	/// <meta name="MobileOptimized" content="device-width">
	$tags = '
		<meta name="viewport" content="width=device-width, minimum-scale=1, initial-scale=1"/>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
	';
	return $tags;
}

function faviconTags($args = []){
	extract(wp_parse_args($args, ['bgcolor'    => '5bbad5',
								  'themecolor' => 'ffffff']));
	$bgcolor    = '#'.ltrim($bgcolor, '#');//not sure why these are different.
	$themecolor = '#'.ltrim($themecolor, '#');
	$tags       = '
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/site.webmanifest">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="'.$bgcolor.'">
	<meta name="msapplication-TileColor" content="'.$bgcolor.'">
	<meta name="theme-color" content="'.$themecolor.'">
	';
	return $tags;
}

function iframe($args){
	extract(wp_parse_args($args, ['url'          => '',
								  'scrolling'    => 'no', //yes no auto
								  'width'        => '100%',
								  'height'       => '500',
								  'frameborder'  => '0',
								  'marginheight' => '0',
								  'div_id'       => 'html_iframe_divid',
								  'div_class'    => 'html_iframe_div',
								  'name'         => 'iframename',
								  'attributes'   => '',
								  'content'      => 'Sorry, your browser doesnt support iframes'// or maybe <a href="'.$url.'" target="_blank">'.$url.'</a>
	]));

	return '<div class="'.$div_class.'" id="'.$div_id.'" ><iframe class="html_iframe" src="'.$url.'" title="" name="'.$name.'" scrolling="'.$scrolling.'" width="'.$width.'" height="'.$height.'" frameborder="'.$frameborder.'" marginheight="'.$marginheight.'" '.$attributes.' >'.$content.'</iframe></div>';
}//add_shortcode('iframe', 'iframeshortcode');


//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
/*$rows[]= array('attribs'=>'class="asdfasdf"'
				 ,array('1','class="sth"')
				 ,'2')  );
$rows[]= array(
				 array('3','class="sth"')
				,array('4','class="sth2"')  );
echo tableFromRows($rows, 'class="oiewuroiweu"' );
*/
// function tableFromRows
//this is only used by tablefromDBrows()  which is  not used
/*
 * function tableFromRows($rows, $args = ''){
	if(is_string($args)){
		$args = ['tableattribs' => $args];
	}//dont really need this, just pass in a string attribs
	extract(wp_parse_args($args, ['tableattribs'    => '',
								  'headerfirst'     => false,
								  'headerlast'      => false,
								  'headerEveryRows' => false]));
	//d($rows, 'start tblefrom rows');
	$out = table($tableattribs);

	$rowsi = 1;
	//d($rows);
	$rowcount = count($rows);
	foreach($rows as $r){
		ehrthick('starrt row');
		//d($r, '$r');
		//$out .= tr($r['rowclass']);
		//ddie();
		//d($r, 'r ');
		foreach($r as $k => $td){
			ehr('start td ra');
			d($k, '$k');
			d($td, '$td');

			if( !is_numeric($k)){// must be 'attribs'
				d([$k, $td], '[$k,td] not numeric');
				continue;
			}
			ddie();
			if(is_string($td) or is_numeric($td)){
				$td = [0 => $td, 1 => ''];
			}

			if(($rowsi == 1 and $headerfirst) or ($headerEveryRows and $rowsi == $headerEveryRows)){
				$header      = wth($td[0], $td[1]);
				$header_last .= wth($td[0], $td[1]);
				$out         .= $header;
			}else{
				$out .= wtd($td[0], $td[1]);
			}
			ddie('');
		}
		//if($rowsi == 1 and $headerfirst) $out .= '<thead>';
		$rowsi++;
		$out .= tr(0);
		if($headerlast and $rowsi == $rowcount + 1 - $headerlast){
			$out .= tr($r['attribs']);
			$out .= $header_last;
			$out .= tr(0);
		}
		ddie('');
		d('continueing!', 'continueing!');
		continue;
	}
	$out .= table(0);
	return $out;
}*/
/*
//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
//this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd


//$rows[]= array( 'col1'=>'asdf'
//				 ,'col2'=>'zxcv'
//				 ,'col3'=>'hjkl');
//$rows[]= array(   'col1'=>'yuio'
//				 ,'col2'=>'rfvrfrv'
//				 ,'col3'=>'hyhnyhn');
//echo wstyle(' #myTableID td{ min-width: 111px;} ');
//echo tableFromDBRows($rows,  array('tableattribs' => ' id="myTableID" class="zebra left_align" '));

function tableFromDBRows($rows, $args = ''){
	if(is_string($args)){
		$args = ['tableattribs' => $args];
	}
	$args = wp_parse_args($args, ['tableattribs'    => '',
								  'headerfirst'     => true,
								  'headerlast'      => false,
								  'customkeys'      => false,
								  'headerEveryRows' => false]);
	extract($args);
	if( !$customkeys){
		foreach($rows as $r){
			$headings = $trs[] = array_keys($r);//might be some assoc array, we just want the first one.
			break;
		}
	}else{
		$headings = $trs[] = $customkeys;
	}
	//d($rows);
	foreach($rows as $k => $v){
		$tds = [];
		foreach($headings as $h){
			//d($rows[$k]);
			if(is_array($rows[$k][$h])){
				$val   = $rows[$k][$h][0];
				$class = $rows[$k][$h][1];
			}else{
				$val   = $rows[$k][$h];
				$class = ' class="sth"	 ';
			}
			$tds[] = [$val, $class];
		}
		$trs[] = $tds;
	}
	//d($trs);
	return tableFromRows($trs, $args);
}

// ^^^^^   this is now olddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
//
*/



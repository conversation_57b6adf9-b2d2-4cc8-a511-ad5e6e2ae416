<?php

//echo getHashtagIgnorer('nav li.menu-item-has-children > a[href="#"], div.wprm-wrapper li.menu-item-has-children  a.wprmenu_parent_item[href="#"]');
// you can add multiples, with comma separttion above is both regular and responsive menu.
function getHashtagIgnorer($element){//#cssVertMenu a.menuwalker
	ob_start();
	echo comment('start getHashtagIgnorer');
	?>
	<script id="getHashtagIgnorer">
		jQuery(document).ready(function ($) {
			var itemm = $('<?=$element?>');
			itemm.unbind('click');// their .click action
			itemm.click(function (e) {
				//itemm.blur();
				//itemm.removeClass("active");
				//document.activeElement = null;
				document.activeElement && document.activeElement.blur();
				e.preventDefault();
				return false;
			});
			itemm.css({"cursor": "default", "text-decoration": "none"});
		});
	</script>
	<?php
	echo comment('END getHashtagIgnorer');

	$ob = ob_get_clean();
	return $ob;
}

function getMenu($menu = 'main', $args = []){
	global $post, $d_post;
	extract(wp_parse_args($args, ['menu_class'     => 'nav top-nav cf',
								  'theme_location' => 'main-nav']));// could do all this other crap too

	$showheadermenu = apply_filters('showheadermenu', true, $post);
	if($showheadermenu){
		$m = wp_nav_menu([//'container'       => 'div',         //remove nav container, we already have one.
						  //'container_class' => 'menu cf',      // class of container (should you choose to use it)
						  //'container_id'    => 'container_iddd', // (should you choose to use it)
						  'menu'           => $menu,            // nav name
						  'menu_class'     => $menu_class,       // adding custom nav class
						  'theme_location' => $theme_location,    // where it's located in the theme
						  'before'         => '',                  // before the menu
						  'after'          => '',                   // after the menu
						  'link_before'    => '',                    // before each link
						  'link_after'     => '',                     // after each link
						  'depth'          => 0,                      // 0 = all.   limit the depth of the nav
						  'fallback_cb'    => '',                     // fallback function (if there is one)
						  'echo'           => false]);
		return $m;
	}
	return '';
}









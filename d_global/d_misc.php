<?php

class d_misc{
	public $menu_parent = 'd_misc';
	public $option      = 'd_misc_options';

	function __construct(){
		add_action('wp', [$this, 'wp'], 99, 0);// want this to run after others, so that we have globals
		add_action('init', [$this, 'init'], 1);

		//add_action('admin_menu',array($this,'admin_menu'), 1);
		add_action('wp_head', [$this, 'fbGoogletags'], 1);
		//add_action('wp_footer',	  		array($this, 'wp_footer'),1);

		add_filter('the_content', [$this, 'fix_autop'], 98);
		add_filter('widget_text', [$this, 'fix_autop'], 98);
		add_filter('wp_headers', [$this, 'remove_x_pingback'], 1, 99);
		add_filter('bloginfo_url', [$this, 'remove_pingback_url'], 10, 2);
		add_filter('body_class', [$this, 'filter_body_class']);
		add_action('wp_insert_comment', [$this, 'filter_wp_insert_comment'], 1, 2);
		//
		add_action('init', [$this, 'remove_global_styles_and_svg_filters']);

		//add_action('wp_enqueue_scripts', [$this, 'remove_wp_block_library_css'], 1000000);
		// idk, this works on enquue not print....
		add_action('wp_print_styles', [$this, 'remove_wp_block_library_css'], 1000);

		add_action('add_meta_boxes', [$this, 'admin_speedup_remove_post_meta_box']);
		add_action('admin_head', [$this, 'userstuff']);
		add_action('after_setup_theme', [$this, 'after_setup_theme']);
	}

	function after_setup_theme(){
		//d('after_setup_theme', 'after_setup_theme');

		/* 2024-11-30 this was annoying me and i dont think it was needed anyway.
		 * add_filter('wp_title', function($title, $sep, $seplocation){
			global $page, $paged;

			if(is_feed()){
				return $title;
			}

			if('right' == $seplocation){
				$title .= get_bloginfo('name');
			}else{
				$title = get_bloginfo('name').$title;
			}

			$site_description = get_bloginfo('description', 'display');
			//is_front_page() Site Front Page --settings/reading
			//is_home() Blog Posts Index --also settings/reading so, pretty stupid.
			if($site_description && (is_home() || is_front_page())){
				$title .= " {$sep} {$site_description}";
			}

			if($paged >= 2 || $page >= 2){
				$title .= " {$sep} ".sprintf(__('Page %s', 'dbt'), max($paged, $page));
			}

			return $title;
		}, 10, 3);   // A better title// http://www.deluxeblogtips.com/2012/03/better-title-meta-tag.html
		*/

		add_filter('the_generator', function(){ return ''; });// remove WP version from RSS
		add_filter('wp_head', function(){// remove injected CSS for recent comments widget
			if(has_filter('wp_head', 'wp_widget_recent_comments_style')){
				remove_filter('wp_head', 'wp_widget_recent_comments_style');
			}
		}, 1); // remove pesky injected css for recent comments widget
		add_action('wp_head', function(){// remove injected CSS from recent comments widget
			global $wp_widget_factory;
			if(isset($wp_widget_factory->widgets['WP_Widget_Recent_Comments'])){
				remove_action('wp_head', [$wp_widget_factory->widgets['WP_Widget_Recent_Comments'], 'recent_comments_style']);
			}
		}, 1);// clean up comment styles in the head
		add_filter('gallery_style', function($css){
			return preg_replace("!<style type='text/css'>(.*?)</style>!s", '', $css);
		});//// remove injected CSS from gallery // clean up gallery output

		add_action('widgets_init', function(){
			register_sidebar(['id'            => 'sidebar1',
							  'name'          => __('Sidebar 1', 'bonestheme'),
							  'description'   => __('The first (primary) sidebar.', 'bonestheme'),
							  'before_widget' => '<div id="%1$s" class="widget %2$s">',
							  'after_widget'  => '</div>',
							  'before_title'  => '<h4 class="widgettitle">',
							  'after_title'   => '</h4>',]);
			/* 	to add more sidebars or widgetized areas, just copy and edit the above sidebar code. In order to call your new sidebar just use the following code:
				 Just change the name to whatever your new 	sidebar's id is, for example:

			register_sidebar(array(
				'id' => 'sidebar2',
				'name' => __( 'Sidebar 2', 'bonestheme' ),
				'description' => __( 'The second (secondary) sidebar.', 'bonestheme' ),
				'before_widget' => '<div id="%1$s" class="widget %2$s">',
				'after_widget' => '</div>',
				'before_title' => '<h4 class="widgettitle">',
				'after_title' => '</h4>',
			));

			To call the sidebar in your template, you can just copy 	the sidebar.php file and rename it to your sidebar's name.
			So using the above example, it would be: 	sidebar-sidebar2.php   */
		});// adding sidebars to Wordpress

		add_filter('the_content', [$this, 'cleanup_peas'], 99);

		// This removes the annoying […] to a Read More link
		add_filter('excerpt_more', function($more){
			global $post;
			return '...  <a class="excerpt-read-more" href="'.get_permalink($post->ID).'" title="'.__('Read ', 'bonestheme').esc_attr(get_the_title($post->ID)).'">'.__('Read more &raquo;', 'bonestheme').'</a>';
		});
	}

	function cleanup_peas($content){
		// cleaning up random code, p tags, around images   (http://css-tricks.com/snippets/wordpress/remove-paragraph-tags-from-around-images/)
		return preg_replace('/<p>\s*(<a .*>)?\s*(<img .* \/>)\s*(<\/a>)?\s*<\/p>/iU', '\1\2\3', $content);
	}

	function userstuff(){
		echo wscript_JQ_docready("
			$('form#your-profile > h3:first').hide();
			$('form#your-profile > table:first').hide();
			$('form#your-profile').show();
			$('tr.user-url-wrap').hide();
			$('tr.user-googleplus-wrap').hide();
			$('tr.user-twitter-wrap').hide();
			$('tr.user-facebook-wrap').hide();
			$('tr.user-description-wrap').hide();
			$('tr.user-profile-picture').hide();
			$('div.yoast.yoast-settings').hide();
			$('div.rwmb-tel-wrapper').hide();
			$('div.rwmb-post-wrapper').hide();
			$('table#fieldset-billing').hide();
			$('table#fieldset-shipping').hide();
		", ' id="userstuff" ');
	}


	//https://css-tricks.com/swapping-a-wordpress-core-meta-box-to-speed-up-editing/
	function admin_speedup_remove_post_meta_box(){// didnt do much...
		global $post_type;
		remove_meta_box('trackbacksdiv', $post_type, 'normal');
		remove_meta_box('postcustom', $post_type, 'normal');
		remove_meta_box('commentstatusdiv', $post_type, 'normal');
		remove_meta_box('commentsdiv', $post_type, 'normal');
		remove_meta_box('revisionsdiv', $post_type, 'normal');
		remove_meta_box('authordiv', $post_type, 'normal');
		remove_meta_box('sqpt-meta-tags', $post_type, 'normal');
	}

	//REMOVE GUTENBERG BLOCK LIBRARY CSS FROM LOADING ON FRONTEND
	function remove_wp_block_library_css(){
		$tags = ['classic-theme-styles-inline-css',
				 'wp-block-library',
				 'wp-block-library-theme',
				 'wc-block-style',
				 'wp-block-library-css',
				 'global-styles',
				 'classic-theme-styles',
				 'contact-form-7'];
		foreach($tags as $tag){
			wp_deregister_style($tag);
			wp_dequeue_style($tag);
			wp_deregister_script($tag);
			wp_dequeue_script($tag);
		}
		//		wp_deregister_style('classic-theme-styles-inline-css'); // REMOVE THEME.JSON
		//		wp_dequeue_style('wp-block-library');
		//		wp_dequeue_style('wp-block-library-theme');
		//		wp_dequeue_style('wc-block-style'); // REMOVE WOOCOMMERCE BLOCK CSS
		//		wp_dequeue_style('wp-block-library-css');
		//		wp_dequeue_style('global-styles'); // REMOVE THEME.JSON
		//		wp_dequeue_style('classic-theme-styles');// which of these is it? this worked
		//		wp_dequeue_style('contact-form-7');
		// no dont do this wp_deregister_style('dashicons');

	}

	// Remove Global Styles and SVG Filters from WP 5.9.1 - 2022-02-27
	function remove_global_styles_and_svg_filters(){
		remove_action('wp_enqueue_scripts', 'wp_enqueue_global_styles');
		remove_action('wp_body_open', 'wp_global_styles_render_svg_filters');
	}


	// This snippet removes the Global Styles and SVG Filters that are mostly if not only used in Full Site Editing in WordPress 5.9.1+
	// Detailed discussion at: https://github.com/WordPress/gutenberg/issues/36834
	// WP default filters: https://github.com/WordPress/WordPress/blob/7d139785ea0cc4b1e9aef21a5632351d0d2ae053/wp-includes/default-filters.php

	function filter_body_class($classes){
		//d($classes, '$classes');
		$ismob = isMobile();//d($ismob, '$ismob');
		$mob   = ($ismob) ? 'is_mobile' : 'not_mobile';
		//is_front_page() Site Front Page --settings/reading
		//is_home() Blog Posts Index --also settings/reading so, pretty stupid.
		$front = (is_front_page()) ? 'is_front_page' : 'not_is_front_page';
		//$home     = (is_home()) ? 'is_home' : 'not_is_home';
		$is_dev = (is_dev()) ? 'is_dev' : 'not_is_dev';

		$s         = getPostSlug(); //d($s, '$s');
		$classes[] = 'slug_'.$s;
		$classes[] = $mob;
		$classes[] = $front;
		$classes[] = $is_dev;
		//$classes[] = $home;
		if(defined('localAlphaBetaLive')){
			$classes[] = 'localAlphaBetaLive_'.localAlphaBetaLive;
		}
		//d($classes, '$classes');
		return $classes;
	}

	function multisite_body_classes($classes){// is not in use??
		$id        = get_current_blog_id();
		$slug      = strtolower(str_replace(' ', '-', trim(get_bloginfo('name'))));
		$classes[] = $slug;
		$classes[] = 'site-id-'.$id;
		return $classes;
	}


	function filter_wp_insert_comment($id, $comment){// deletes any comment  containing an url.  could add a filter to override this.
		//d($id);//d($comment->comment_author_url);
		if($comment->comment_author_url != ''){
			wp_delete_comment($id, true);
		}//force delte  --this is right after it's inserted, so you can check and dlete here.  i cant find a way to *block it from* inserting.
		//die('filter_wp_insert_comment');
	}

	/*add_action('comment_form', array($this, 'add_honeypot'));
	add_filter('pre_comment_approved', array($this, 'check_honeypot'));*/
	/* function add_honeypot($postID) {// this one winds it up in the spam...it works, but then youre full of spam.  cuould use this along with the other.  only brendaberry is using this at all. yet.
		$textarea_name = 'your_url';
		echo '<p style="display:none">';
		echo '<textarea name="' . $textarea_name . '" cols="100%" rows="10"></textarea>';
		echo '<label  for="' . $textarea_name . '">' . 'If you are a human, do not fill in this field.' . '</label>';
		echo '</p>';

	}
	function check_honeypot($approved) {
		$textarea_name = 'your_url';
		if (!empty($_POST[$textarea_name])) {// Bot filled out the hidden textarea
			$approved = 'spam';
		}
		return $approved;
	}*/

	function init(){
		//add_theme_support('menus');// works without this but the menu page says 'your theme dont support menus'
		add_theme_support('post-thumbnails');         //actually you do need this.


		//<editor-fold desc="-- image sizes   -- ">
		// thumbnail 	150 x 150 	no crop
		// medium 		360 x 0 	nocrop this replaces the gallerythumb_cols_3
		// medium_large 768 x 0 	nocrop
		// large 		1200 x 0 	nocrop this replaces the wrapperwidth
		add_image_size('medium_large', 768, 0, false);//this may be redundant
		$badsizes = getBadSizes();                    //$sizes = get_intermediate_image_sizes(); //dlist($image_sizes, '$image_sizes');
		foreach($badsizes as $size){
			remove_image_size($size);// this doesnt actually do much.  althought it could be the order in which this is loaded.
		}
		// this is what akshully does it.  only runs when you upload sth!!!!!!!!!!!!!!!!!!!!!!!!!!!
		add_filter('intermediate_image_sizes_advanced', function($sizes){
			$badsizes = getBadSizes(); //d($sizes, '$sizes d_misc');
			foreach($badsizes as $size){
				unset($sizes[$size]);
			} //d($sizes, '$sizes d_misc after');
			return $sizes;
		});
		add_filter('intermediate_image_sizes', function($sizes){
			foreach(getBadSizes() as $size){
				unset($sizes[$size]);
			}
			return $sizes;
		});

		// image sizes img sizes intermediate sizes.
		/* The `-scaled` suffix is automatically added by WordPress when an uploaded image exceeds the maximum dimensions
				defined in the `big_image_size_threshold` filter. This filter is 	defined in the core for images.
				By default, this dimension is set to **2560 pixels**. If your image exceeds this size, WordPress will create a scaled-down version of the image
				and append `-scaled` to its filename. since 5.3
		*/
		add_filter('big_image_size_threshold', function($threshold){
			return 3000; // Your custom size threshold. you can also return false.
		});

		//you can add or substract here. puts the sizes in the 'insert media'
		add_filter('image_size_names_choose', function($sizes){
			$custom_sizes = ['medium_large' => 'medium_large',];
			$sizes        = array_merge($sizes, $custom_sizes);
			$sizes        = array_unique($sizes);
			$sizes        = array_unique($sizes, SORT_REGULAR);//the keys
			// cant do debugging in here cuz ajax  arraywritetofile('sizesdelteme.php', 'dvar', $sizes);
			$badsizes = getBadSizes();
			foreach($badsizes as $size){
				unset($sizes[$size]);
			}
			// cant do debugging in here cuz ajax  arraywritetofile('sizesdelteme2.php', 'dvar', $sizes);
			return $sizes;
		});

		// set up our sizes.  change the dfaults here.
		if(d_OnlyOnce('d_image_sizes', '2024-07-25.asdfwe')){
			update_option('thumbnail_size_w', 150);
			update_option('thumbnail_size_h', 0);
			update_option('thumbnail_crop', 0);

			update_option('medium_size_w', 360);
			update_option('medium_size_h', 0);

			update_option('large_size_w', 1200);
			update_option('large_size_h', 0);

			update_option('medium_large_size_w', 768);
			update_option('medium_large_size_h', 0);

			// remove these.
			update_option('1536x1536_size_w', 0);//setting w/h to zero removes the size.
			update_option('1536x1536_size_h', 0);
			update_option('2048x2048_size_w', 0);
			update_option('2048x2048_size_h', 0);
		}

		//</editor-fold> -- image sizes --

		$this->wphead_cleanup();                        //
		remove_filter('the_title', 'capital_P_dangit', 11);
		remove_filter('the_content', 'capital_P_dangit', 11);
		remove_filter('comment_text', 'capital_P_dangit', 31);

		add_post_type_support('page', 'excerpt');
		wp_enqueue_script('d_global_utils', urltoglobaljs.'utils.js', ['jquery']);//  debugging stuff mostly.

		remove_action('wp_head', 'print_emoji_detection_script', 7);
		remove_action('admin_print_scripts', 'print_emoji_detection_script');
		remove_action('wp_print_styles', 'print_emoji_styles');
		remove_filter('the_content_feed', 'wp_staticize_emoji');
		remove_action('admin_print_styles', 'print_emoji_styles');
		remove_filter('comment_text_rss', 'wp_staticize_emoji');
		remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
		//add_filter( 'tiny_mce_plugins', 'disable_emojis_tinymce' );
		remove_action('wp_head', 'feed_links_extra', 3);                          // category feeds
		remove_action('wp_head', 'feed_links', 2);                                // post and comment feeds

		remove_action('wp_head', 'rsd_link');
		remove_action('wp_head', 'wlwmanifest_link');                      // windows live writer
		remove_action('wp_head', 'parent_post_rel_link', 10, 0);           // previous link
		remove_action('wp_head', 'start_post_rel_link', 10, 0);            // start link
		remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);// links for adjacent posts
		remove_action('wp_head', 'wp_generator');                          // WP version
		//these are the same func:
		add_filter('style_loader_src', function($src){
			if(strpos($src, 'ver=')){
				$src = remove_query_arg('ver', $src);
			}
			return $src;
		}, 9999);// remove WP version from css
		add_filter('script_loader_src', function($src){
			if(strpos($src, 'ver=')){
				$src = remove_query_arg('ver', $src);
			}
			return $src;
		}, 9999);// remove Wp version from scripts
	}


	function remove_x_pingback($headers){
		unset($headers['X-Pingback']);
		return $headers;
	}

	function remove_pingback_url($output, $show){
		if($show == 'pingback_url'){
			return '';
		}
		return $output;
	}


	function admin_menu(){//not used yet
		$args = ['page_title' => 'Page Title d_misc',
				 'menu_title' => 'Menu Title d_misc',
				 'menu_slug'  => $this->menu_parent,
				 'position'   => 70];
		//createTopLevelMenuItem($args);  dont need this yet
	}

	function wp(){
		// wp is the first hook with $post
		global $post, $posts;//d($post, '$post wp');//	d($posts, '$posts');

		$GLOBALS['resultspage'] = (count($posts) > 1);
		$meta                   = getFormattedMeta($post);//wordpress meta //	d($meta['fullwidth'], '$meta[fullwidth]');
		$GLOBALS['fullwidth']   = $meta['fullwidth'];//d($GLOBALS['fullwidth'], '$GLOBALS[fullwidth]');

		add_filter('body_class', function($classes){
			$classes[] = ($GLOBALS['resultspage']) ? 'resultspage' : '';
			$classes[] = ($GLOBALS['fullwidth']) ? 'fullwidth' : '';//now we dont need the full width beaver tpl.
			return $classes;
		});

		add_filter('post_class', function($classes, $class, $post_id){
			//d($classes, '$classes'); //d($class, '$class');// the current that is actually in the post_class() call   post_class('some classes ');
			// dont need this usually. but can be good for other kinda stuff
			//$classes[] = has_category(['podcasts']) ? ' yespodcasts ' : 'nopodcast';// the current post
			$classes[] = ($GLOBALS['resultspage']) ? 'resultspage' : 'not_resultspage';
			return $classes;
		}, 10, 3);

		add_action('start_body_wrapper', function(){// its in my header.php
			if(is_404()){
				echo wdiv('404... Nada.', ' style="margin:0 auto;" ');
			}
		}, 0);
		//
	}

	function wphead_cleanup(){
		remove_action('wp_head', 'rsd_link');
		remove_action('wp_head', 'wp_generator');
		remove_action('wp_head', 'feed_links', 2);
		remove_action('wp_head', 'feed_links_extra', 3);
		remove_action('wp_head', 'index_rel_link');
		remove_action('wp_head', 'wlwmanifest_link');
		remove_action('wp_head', 'parent_post_rel_link', 10, 0);
		remove_action('wp_head', 'start_post_rel_link');
		remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');
		remove_action('wp_head', 'wp_shortlink_wp_head', 10, 0);
	}

	function fix_autop($content){
		//dhtml($content);
		$html = trim($content);
		//$blocktags = 'address|article|aside|audio|blockquote|canvas|caption|center|col|del|dd|div|dl|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|header|hgroup|input|iframe|ins|img|li|nav|noframes|noscript|object|ol|output|pre|script|style|span|section|table|tbody|td|tfoot|thead|th|tr|ul|video';
		//had to take span out...
		$blocktags = 'address|article|aside|audio|blockquote|canvas|caption|center|col|del|dd|div|dl|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|header|hgroup|input|iframe|ins|img|li|nav|noframes|noscript|object|ol|output|pre|script|style|section|table|tbody|td|tfoot|thead|th|tr|ul|video';
		$html      = preg_replace('~<p>\s*<('.$blocktags.')\b~i', '<$1', $html);
		$html      = preg_replace('~</('.$blocktags.')>\s*</p>~i', '</$1>', $html);

		$html = preg_replace('/<p>(\s*)<!--/', '<!--', $html);// leave comments alone
		$html = preg_replace('|-->(\s*)</p>|', '-->', $html);

		return $html;
		/*to undo the fix:
		add_filter('the_content', 	 'unfix_autop',90);
		add_filter('widget_text', 	'unfix_autop',90);
		function unfix_autop($content) {
			global $emuMiscManagers;
			$m = $emuMiscManagers->getManager('emuM_misc');//d($m); //dhtml($content);
			if(haystackcontainsneedle($content,'et_pb_')){                                  -- or whateverr other reason to remove the filter.  et_pb is for elegant themes page builder, it relies on stupid <p> tags
				$d = remove_filter( 'the_content', array($m, 'fix_autop'),98 );//d($d);
				remove_filter( 'widget_text', array($m, 'fix_autop') );
			}
			return $content;
		}
		*/
	}

	function fbGoogletags(){ //add_action('wp_head',	  		array($this, 'fbGoogletags'),1);
		// yoast doesnt do it as good as this but...

		if(definedandtrue('no_fbGoogletags')){
			echo comment('no_fbGoogletags() - overriden by constant. d_misc.php - this runs on wp_head');
			return;
		}

		if( !function_exists('getThemeOption')){//this is just so calls dont bork, if i have to disable redux cuz of conflicts.
			function getThemeOption($adsf = ''){
				return '';
			}
		}
		global $post;

		$fbadmin = getThemeOption('fbadmin');
		$fbappid = (defined('fbappid')) ? fbappid : '';

		//	getSiteDesc()  these are good 2025
		//	getSiteName()
		$sitename = getSiteName();//do these up here, need them numerous.
		$sitedesc = getSiteDesc();

		$defaultpages = ['home', 'blog', 'store', 'reviews', 'contact', 'portfolio', 'about', 'library', 'portfolio'];//could put a filter in here

		//is_front_page() Site Front Page --settings/reading
		//is_home() Blog Posts Index --also settings/reading so, pretty stupid.
		$searchOrFront = (is_search() or is_front_page()) ? true : false;                                             //d($searchOrFront);
		$defaultpage   = (in_array($post->post_name, $defaultpages)) ? true : false;                                  //d($defaultpage);
		$specific      = ($searchOrFront or $defaultpage) ? false : true;                                             // either of those is true, then specific is false
		$specific      = (is_null($post)) ? false : $specific;
		//d($post);
		//d($specific, 'SPECIFIC?');
		//d(whatis_this());

		if($specific){// probly should be testing for is_cat(), is_tax(), etc
			//<editor-fold desc="-- specific -- ">
			$startcomment = '<!-- this is a post: title:"'.$post->post_title.'", here are the post specific meta  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx  -->'.nl();

			if( !has_post_thumbnail($post->ID) and function_exists('getdefaultFeat')){
				$img = home_url().getdefaultFeat();
			}else{
				$img = getOneOfTheseFeaturedPostSizes($post->ID, ['fb', 'large', 'full', 'medium', 'thumbnail']);//$img will be false if getOneOfTheseFeaturedPostSizes() fails
				if( !$img){
					$img = getThemeOption('fbhomeimg');
					if(is_array($img)){
						$img = $img['url'];
					}
				}
				// could use this, but currently no-one is.
				//function fbGoogletags_feat($img, $post){
				//	 return $img;
				//}add_filter( 'fbGoogletags_feat', 'filter_footeroverride', 5, 2 );
				//$img = apply_filters('fbGoogletags_feat', $img, $post);

			}
			$title = stripContent(getPostHeaderOrTitle($post->ID));
			$title = apply_filters('fb_title_override', $title, $post);
			$url   = get_permalink($post->ID);
			$url   = curPageURL();

			$desc = getShortOrExcerptFbDesc($post);
			if(empty($desc)){
				$desc = getSiteDesc();
			}
			if(empty($desc)){
				$desc = getThemeOption('fbdescription');
			}
			$desc = apply_filters('seo_description_override', $desc, $post);

			$type       = "article";
			$endcomment = '<!-- end post specific xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx  -->'.nl();
			//</editor-fold> -- specific --
		}else{
			$startcomment = '<!-- this is a default, here are the home meta xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx  -->'.nl();
			$img          = getThemeOption('fbhomeimg');
			if(is_array($img)){//not sure why this just started being an array...
				$img = $img['url'];
			}
			$title      = $sitename;
			$url        = home_url();
			$desc       = $sitedesc;
			$type       = "article";
			$endcomment = '<!-- end home meta xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx  -->'.nl();
		}
		//d($img);

		//add_filter( 'img_in_fbGoogletags', function ($img, $post)
		$img = apply_filters('img_in_fbGoogletags', $img, $post);

		ob_start();
		enl();
		enl();
		echo '<!-- fbGoogletags() -->'.nl().'<!--  post type: '.get_post_type().' -->'.nl();
		echo $startcomment; ?>
		<!-- facebook -->
		<meta property="og:site_name" content="<?=quotesDoubleToSingle($sitename)?>"/>
		<meta property="og:title" content="<?=quotesDoubleToSingle($title)?>"/>
		<meta property="og:url" content="<?=quotesDoubleToSingle($url)?>"/>
		<meta property="og:description" content="<?=quotesDoubleToSingle($desc)?>"/>
		<meta property="og:image" content="<?=quotesDoubleToSingle($img)?>"/>
		<meta property="og:type" content="<?=quotesDoubleToSingle($type)?>"/>
		<!--<meta property="fb:admins" content="<?php /*=quotesDoubleToSingle($fbadmin)*/ ?>"/>-->
		<?=(defined('FB_APP_ID')) ? '<meta property="fb:app_id" content="'.FB_APP_ID.'"/>' : ''?>

		<!-- google plus -->
		<meta itemprop="name" content="<?=quotesDoubleToSingle($title)?>">
		<meta itemprop="description" content="<?=quotesDoubleToSingle($desc)?>">
		<meta itemprop="image" content="<?=quotesDoubleToSingle($img)?>">

		<!-- 	<meta property="fb_app_id" content="<?=quotesDoubleToSingle($fbappid)?>" />  -->

		<?=$endcomment?>
		<?php

		$tags = ob_get_clean();

		echo $tags;
		echo '<!-- END fbGoogletags() -->';
		enl();
		enl();
	}//fbGoogletags()


}// class sssssssssssssssssssssssssssssssssssssssssssssssssssssssssss

function getBadSizes(){
	$badsizes = [// use these: 'thumbnail', // 'medium',  // 'medium_large',  // 'large',
				 // 'gallerythumb_cols_3', old
				 // 'gallerythumb_cols_4',old
				 // 'wrapperwidth',old  replaced with just large
				 '1536x1536',
				 '2048x2048',
				 'woocommerce_thumbnail',
				 'woocommerce_single',
				 'woocommerce_gallery_thumbnail',];
	return $badsizes;
	// could put a filter for this.
}







@font-face {
	font-family: 'icomoon';
	src: url('fonts/icomoon.eot?5ujmx2');
	src: url('fonts/icomoon.eot?5ujmx2#iefix') format('embedded-opentype'),
	url('fonts/icomoon.ttf?5ujmx2') format('truetype'),
	url('fonts/icomoon.woff?5ujmx2') format('woff'),
	url('fonts/icomoon.svg?5ujmx2#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="wpr-icon-"], [class*=" wpr-icon-"] {
	/* use !important to prevent issues with browser extensions that change fonts */
	font-family: san-serif, serif !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wpr-icon-store:before {
	content: "\e900";
}

.wpr-icon-cart2:before {
	content: "\e901";
}

.wpr-icon-basket:before {
	content: "\e902";
}

.wpr-icon-cart:before {
	content: "\e93a";
}

.wpr-icon-x:before {
	content: "\e687";
}

.wpr-icon-rss:before {
	content: "\e600";
}

.wpr-icon-rss-alt:before {
	content: "\e68c";
}

.wpr-icon-mail:before {
	content: "\e68d";
}

.wpr-icon-plus:before {
	content: "\e601";
}

.wpr-icon-minus:before {
	content: "\e602";
}

.wpr-icon-magnifying-glass:before {
	content: "\e603";
}

.wpr-icon-instagram:before {
	content: "\e688";
}

.wpr-icon-grid:before {
	content: "\e604";
}

.wpr-icon-search:before {
	content: "\e605";
}

.wpr-icon-twitter:before {
	content: "\e606";
}

.wpr-icon-plus2:before {
	content: "\e607";
}

.wpr-icon-minus2:before {
	content: "\e608";
}

.wpr-icon-search2:before {
	content: "\e609";
}

.wpr-icon-feed:before {
	content: "\e60a";
}

.wpr-icon-cancel:before {
	content: "\e60b";
}

.wpr-icon-home:before {
	content: "\e60c";
}

.wpr-icon-home2:before {
	content: "\e60d";
}

.wpr-icon-search3:before {
	content: "\e689";
}

.wpr-icon-menu:before {
	content: "\e60e";
}

.wpr-icon-point-right:before {
	content: "\e60f";
}

.wpr-icon-point-down:before {
	content: "\e610";
}

.wpr-icon-close:before {
	content: "\e611";
}

.wpr-icon-minus3:before {
	content: "\e612";
}

.wpr-icon-plus3:before {
	content: "\e613";
}

.wpr-icon-arrow-right:before {
	content: "\e614";
}

.wpr-icon-arrow-down:before {
	content: "\e615";
}

.wpr-icon-arrow-right2:before {
	content: "\e616";
}

.wpr-icon-arrow-down2:before {
	content: "\e617";
}

.wpr-icon-arrow-right3:before {
	content: "\e618";
}

.wpr-icon-arrow-down3:before {
	content: "\e619";
}

.wpr-icon-paragraph-justify:before {
	content: "\e61a";
}

.wpr-icon-paragraph-justify2:before {
	content: "\e61b";
}

.wpr-icon-mail2:before {
	content: "\e61c";
}

.wpr-icon-google:before {
	content: "\e61d";
}

.wpr-icon-googleplus:before {
	content: "\e61e";
}

.wpr-icon-googleplus2:before {
	content: "\e61f";
}

.wpr-icon-googleplus3:before {
	content: "\e620";
}

.wpr-icon-googleplus4:before {
	content: "\e621";
}

.wpr-icon-facebook:before {
	content: "\e622";
}

.wpr-icon-facebook2:before {
	content: "\e623";
}

.wpr-icon-facebook3:before {
	content: "\e624";
}

.wpr-icon-instagram2:before {
	content: "\e68a";
}

.wpr-icon-twitter2:before {
	content: "\e625";
}

.wpr-icon-twitter3:before {
	content: "\e626";
}

.wpr-icon-twitter4:before {
	content: "\e627";
}

.wpr-icon-feed2:before {
	content: "\e628";
}

.wpr-icon-feed3:before {
	content: "\e629";
}

.wpr-icon-feed4:before {
	content: "\e62a";
}

.wpr-icon-vimeo:before {
	content: "\e62b";
}

.wpr-icon-vimeo2:before {
	content: "\e62c";
}

.wpr-icon-vimeo3:before {
	content: "\e62d";
}

.wpr-icon-flickr:before {
	content: "\e62e";
}

.wpr-icon-flickr2:before {
	content: "\e62f";
}

.wpr-icon-flickr3:before {
	content: "\e630";
}

.wpr-icon-flickr4:before {
	content: "\e631";
}

.wpr-icon-deviantart:before {
	content: "\e632";
}

.wpr-icon-deviantart2:before {
	content: "\e633";
}

.wpr-icon-github:before {
	content: "\e634";
}

.wpr-icon-github2:before {
	content: "\e635";
}

.wpr-icon-github3:before {
	content: "\e636";
}

.wpr-icon-github4:before {
	content: "\e637";
}

.wpr-icon-github5:before {
	content: "\e638";
}

.wpr-icon-tumblr:before {
	content: "\e639";
}

.wpr-icon-tumblr2:before {
	content: "\e63a";
}

.wpr-icon-skype:before {
	content: "\e63b";
}

.wpr-icon-linkedin:before {
	content: "\e63c";
}

.wpr-icon-pinterest:before {
	content: "\e63d";
}

.wpr-icon-pinterest2:before {
	content: "\e63e";
}

.wpr-icon-search32:before {
	content: "\e63f";
}

.wpr-icon-basket2:before {
	content: "\e903";
}

.wpr-icon-basket3:before {
	content: "\e027";
}

.wpr-icon-magnifying-glass2:before {
	content: "\e037";
}

.wpr-icon-facebook5:before {
	content: "\e05d";
}

.wpr-icon-twitter5:before {
	content: "\e05e";
}

.wpr-icon-googleplus5:before {
	content: "\e05f";
}

.wpr-icon-rss2:before {
	content: "\e060";
}

.wpr-icon-tumblr3:before {
	content: "\e061";
}

.wpr-icon-linkedin2:before {
	content: "\e062";
}

.wpr-icon-dribbble:before {
	content: "\e063";
}

.wpr-icon-plus5:before {
	content: "\e656";
}

.wpr-icon-plus6:before {
	content: "\e657";
}

.wpr-icon-minus5:before {
	content: "\e658";
}

.wpr-icon-minus6:before {
	content: "\e659";
}

.wpr-icon-cancel2:before {
	content: "\e65a";
}

.wpr-icon-magnifier:before {
	content: "\e65b";
}

.wpr-icon-grid3:before {
	content: "\e65c";
}

.wpr-icon-grid4:before {
	content: "\e65d";
}

.wpr-icon-list:before {
	content: "\e65e";
}

.wpr-icon-add:before {
	content: "\e65f";
}

.wpr-icon-minus7:before {
	content: "\e660";
}

.wpr-icon-search7:before {
	content: "\e661";
}

.wpr-icon-shopping-bag2:before {
	content: "\e904";
}

.wpr-icon-shopping-cart2:before {
	content: "\e905";
}

.wpr-icon-add_shopping_cart:before {
	content: "\e854";
}

.wpr-icon-shopping_cart:before {
	content: "\e8cc";
}

.wpr-icon-remove_shopping_cart:before {
	content: "\e928";
}

.wpr-icon-shopping_basket:before {
	content: "\e8cb";
}

.wpr-icon-align-justify:before {
	content: "\e026";
}

.wpr-icon-search5:before {
	content: "\e036";
}

.wpr-icon-circle-plus:before {
	content: "\e040";
}

.wpr-icon-circle-minus:before {
	content: "\e041";
}

.wpr-icon-square-plus:before {
	content: "\e044";
}

.wpr-icon-square-minus:before {
	content: "\e045";
}

.wpr-icon-arrow-right4:before {
	content: "\e095";
}

.wpr-icon-arrow-down4:before {
	content: "\e097";
}

.wpr-icon-grid2:before {
	content: "\e102";
}

.wpr-icon-cross:before {
	content: "\e117";
}

.wpr-icon-menu2:before {
	content: "\e120";
}

.wpr-icon-ellipsis:before {
	content: "\e129";
}

.wpr-icon-times:before {
	content: "\e647";
}

.wpr-icon-plus4:before {
	content: "\e648";
}

.wpr-icon-minus4:before {
	content: "\e649";
}

.wpr-icon-arrow-right-thick:before {
	content: "\e64a";
}

.wpr-icon-th-menu:before {
	content: "\e64b";
}

.wpr-icon-arrow-down-thick:before {
	content: "\e64c";
}

.wpr-icon-times-outline:before {
	content: "\e64d";
}

.wpr-icon-plus-outline:before {
	content: "\e64e";
}

.wpr-icon-minus-outline:before {
	content: "\e64f";
}

.wpr-icon-th-small-outline:before {
	content: "\e650";
}

.wpr-icon-th-menu-outline:before {
	content: "\e651";
}

.wpr-icon-zoom-outline:before {
	content: "\e652";
}

.wpr-icon-arrow-down5:before {
	content: "\e653";
}

.wpr-icon-arrow-right5:before {
	content: "\e654";
}

.wpr-icon-search6:before {
	content: "\e655";
}

.wpr-icon-shopping-cart:before {
	content: "\f07a";
}

.wpr-icon-cart-plus:before {
	content: "\f217";
}

.wpr-icon-cart-arrow-down:before {
	content: "\f218";
}

.wpr-icon-shopping-bag:before {
	content: "\f290";
}

.wpr-icon-shopping-basket:before {
	content: "\f291";
}

.wpr-icon-search9:before {
	content: "\f002";
}

.wpr-icon-envelope-o:before {
	content: "\f003";
}

.wpr-icon-th-large:before {
	content: "\f009";
}

.wpr-icon-th:before {
	content: "\f00a";
}

.wpr-icon-th-list:before {
	content: "\f00b";
}

.wpr-icon-times2:before {
	content: "\f00d";
}

.wpr-icon-home3:before {
	content: "\f015";
}

.wpr-icon-align-justify2:before {
	content: "\f039";
}

.wpr-icon-chevron-right:before {
	content: "\f054";
}

.wpr-icon-plus-circle:before {
	content: "\f055";
}

.wpr-icon-minus-circle:before {
	content: "\f056";
}

.wpr-icon-arrow-right6:before {
	content: "\f061";
}

.wpr-icon-arrow-down6:before {
	content: "\f063";
}

.wpr-icon-plus7:before {
	content: "\f067";
}

.wpr-icon-minus8:before {
	content: "\f068";
}

.wpr-icon-chevron-down:before {
	content: "\f078";
}

.wpr-icon-twitter-square:before {
	content: "\f081";
}

.wpr-icon-facebook-square:before {
	content: "\f082";
}

.wpr-icon-linkedin-square:before {
	content: "\f08c";
}

.wpr-icon-github-square:before {
	content: "\f092";
}

.wpr-icon-twitter6:before {
	content: "\f099";
}

.wpr-icon-facebook6:before {
	content: "\f09a";
}

.wpr-icon-github6:before {
	content: "\f09b";
}

.wpr-icon-hand-o-right:before {
	content: "\f0a4";
}

.wpr-icon-hand-o-down:before {
	content: "\f0a7";
}

.wpr-icon-arrow-circle-right:before {
	content: "\f0a9";
}

.wpr-icon-arrow-circle-down:before {
	content: "\f0ab";
}

.wpr-icon-tasks:before {
	content: "\f0ae";
}

.wpr-icon-bars:before {
	content: "\f0c9";
}

.wpr-icon-pinterest3:before {
	content: "\f0d2";
}

.wpr-icon-pinterest-square:before {
	content: "\f0d3";
}

.wpr-icon-google-plus-square:before {
	content: "\f0d4";
}

.wpr-icon-google-plus:before {
	content: "\f0d5";
}

.wpr-icon-envelope:before {
	content: "\f0e0";
}

.wpr-icon-linkedin3:before {
	content: "\f0e1";
}

.wpr-icon-angle-double-right:before {
	content: "\f101";
}

.wpr-icon-angle-double-down:before {
	content: "\f103";
}

.wpr-icon-angle-right:before {
	content: "\f105";
}

.wpr-icon-angle-down:before {
	content: "\f107";
}

.wpr-icon-github-alt:before {
	content: "\f113";
}

.wpr-icon-ellipsis-h:before {
	content: "\f141";
}

.wpr-icon-rss-square:before {
	content: "\f143";
}

.wpr-icon-toggle-down:before {
	content: "\f150";
}

.wpr-icon-toggle-right:before {
	content: "\f152";
}

.wpr-icon-youtube-square:before {
	content: "\f166";
}

.wpr-icon-youtube:before {
	content: "\f167";
}

.wpr-icon-instagram3:before {
	content: "\f16d";
}

.wpr-icon-apple:before {
	content: "\f179";
}

.wpr-icon-android:before {
	content: "\f17b";
}

.wpr-icon-skype2:before {
	content: "\f17e";
}

.wpr-icon-vimeo-square:before {
	content: "\f194";
}

.wpr-icon-cart3:before {
	content: "\e906";
}

.wpr-icon-bag:before {
	content: "\e907";
}

.wpr-icon-facebook4:before {
	content: "\e640";
}

.wpr-icon-twitter-old:before {
	content: "\e641";
}

.wpr-icon-feed5:before {
	content: "\e642";
}

.wpr-icon-bird:before {
	content: "\e643";
}

.wpr-icon-search4:before {
	content: "\e644";
}

.wpr-icon-pointer:before {
	content: "\e645";
}

.wpr-icon-pointer2:before {
	content: "\e646";
}

.wpr-icon-shopping-bag3:before {
	content: "\e908";
}

.wpr-icon-shopping-basket2:before {
	content: "\e909";
}

.wpr-icon-shopping-cart3:before {
	content: "\e90a";
}

.wpr-icon-search10:before {
	content: "\e68b";
}

.wpr-icon-cross2:before {
	content: "\e663";
}

.wpr-icon-minus9:before {
	content: "\e664";
}

.wpr-icon-plus8:before {
	content: "\e665";
}

.wpr-icon-layout:before {
	content: "\e666";
}

.wpr-icon-list2:before {
	content: "\e667";
}

.wpr-icon-video:before {
	content: "\e668";
}

.wpr-icon-arrow-down7:before {
	content: "\e669";
}

.wpr-icon-arrow-right7:before {
	content: "\e66a";
}

.wpr-icon-arrow-down8:before {
	content: "\e66b";
}

.wpr-icon-arrow-right8:before {
	content: "\e66c";
}

.wpr-icon-arrow-down9:before {
	content: "\e66d";
}

.wpr-icon-arrow-right9:before {
	content: "\e66e";
}

.wpr-icon-arrow-down10:before {
	content: "\e66f";
}

.wpr-icon-arrow-right10:before {
	content: "\e670";
}

.wpr-icon-arrow-down11:before {
	content: "\e671";
}

.wpr-icon-uniE672:before {
	content: "\e672";
}

.wpr-icon-arrow-right11:before {
	content: "\e673";
}

.wpr-icon-github62:before {
	content: "\e674";
}

.wpr-icon-flickr5:before {
	content: "\e675";
}

.wpr-icon-flickr6:before {
	content: "\e676";
}

.wpr-icon-vimeo4:before {
	content: "\e677";
}

.wpr-icon-vimeo5:before {
	content: "\e678";
}

.wpr-icon-twitter7:before {
	content: "\e679";
}

.wpr-icon-facebook7:before {
	content: "\e67a";
}

.wpr-icon-facebook8:before {
	content: "\e67b";
}

.wpr-icon-facebook9:before {
	content: "\e67c";
}

.wpr-icon-googleplus6:before {
	content: "\e67d";
}

.wpr-icon-googleplus7:before {
	content: "\e67e";
}

.wpr-icon-pinterest4:before {
	content: "\e67f";
}

.wpr-icon-pinterest5:before {
	content: "\e680";
}

.wpr-icon-tumblr4:before {
	content: "\e681";
}

.wpr-icon-linkedin4:before {
	content: "\e682";
}

.wpr-icon-linkedin5:before {
	content: "\e683";
}

.wpr-icon-instagram4:before {
	content: "\e684";
}

.wpr-icon-skype3:before {
	content: "\e685";
}

.wpr-icon-skype4:before {
	content: "\e686";
}

.wpr-icon-cart4:before {
	content: "\e90b";
}

.wpr-icon-search8:before {
	content: "\e662";
}


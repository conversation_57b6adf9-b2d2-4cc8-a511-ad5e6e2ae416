@font-face {
	font-family: 'wprmenu';
	src: url('/wp-content/mu-plugins/d_inc/d_mobileMenu/fonts/wprmenu.woff?p8o4s0') format('woff'), /* Most modern browsers */ url('/wp-content/mu-plugins/d_inc/d_mobileMenu/fonts/wprmenu.ttf?p8o4s0') format('truetype'), /* Safari, Android */ url('/wp-content/mu-plugins/d_inc/d_mobileMenu/fonts/wprmenu.eot?p8o4s0') format('embedded-opentype'); /* Older IE */
	font-weight: 400;
	font-style: normal;
}


#wprmenu_bar, #wprmenu_bar * {
	margin: 0;
	padding: 0;
	box-sizing: border-box !important;
	text-align: left;
}

#wprmenu_bar {
	cursor: pointer;
	overflow: hidden;
	z-index: 99999;
}

#wprmenu_bar .bar_logo {
	border: none;
	margin: 0;
	padding: 0px 8px 0px 0px;
	width: auto;
	height: 25px;
}

#wprmenu_bar .wprmenu_icon {
	margin-right: 5px;
}


.wpr_search .wpr-search-field {
	padding: 5px !important;
	border: 1px solid #ccc;
	max-width: 103%;
	height: 35px;
	outline: 0;
}

#wprmenu_bar .wprmenu_icon_menu {
	color: #f2f2f2;
	margin-right: 5px;
}

#wprmenu_menu_ul .sub-menu {
	padding: 0;
}

#wprmenu_menu.wprmenu_levels ul li {
	display: block;
	overflow: hidden;
	border-bottom: 1px solid #131212;
	border-top: 1px solid #474747;
	position: relative;
}

.search-menu {
	padding: 14px;
}

#mg-wprm-wrap [class*=" icon-"],
#mg-wprm-wrap [class^=icon-] {
	font-family: wprmenu !important;
	speak: none;
	font-style: normal;
	font-weight: 400;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}

#mg-wprm-wrap ul li {
	margin: 0
}

#mg-wprm-wrap ul li ul li ul li.wprmenu_parent_item_li, #mg-wprm-wrap ul li ul li.wprmenu_parent_item_li {
	padding-left: 0;
}

#mg-wprm-wrap ul li ul.sub-menu li {
	border: none;
	position: relative;
}

div#mg-wprm-wrap ul li span.wprmenu_icon {
	font-family: 'wprmenu';
	position: absolute;
	right: 10px;
	line-height: 38px;
	padding: 24px;
}

div#mg-wprm-wrap ul#wprmenu_menu_ul > li > span.wprmenu_icon:before {
	right: 3px;
}

div#mg-wprm-wrap ul li span.wprmenu_icon:before {
	top: 3px;
}

div#mg-wprm-wrap ul li ul.sub-menu span.wprmenu_icon {
	margin-left: 0;
}

div#mg-wprm-wrap form.wpr-search-form {
	position: relative;
	padding: 0;
	margin: 0;
	max-width: 600px;
	width: 100%;
}

div#mg-wprm-wrap form.wpr-search-form button.wpr_submit {
	position: absolute;
	top: 0px;
	right: 5px;
	background-color: transparent;
	color: #000;
	width: 35px;
	height: 35px;
	padding: 0 8px;
	border: none;
}

html body #wprmenu_menu_ul li.wprmenu_parent_item_li > ul {
	margin-left: 0 !important;
	padding: 0 10px;
	margin: 0;
}

#mg-wprm-wrap {
	z-index: 9999;
}

#mg-wprm-wrap ul li a {
	text-decoration: none;
	z-index: 9999;
}


.icon_default.wprmenu_icon_par:before {
	content: "\74";
	position: absolute;
}

.icon_default.wprmenu_par_opened:before {
	content: "\6f";
}

.wprmenu_icon {
	position: relative;
	transform: rotate(0);
	transition: .5s ease-in-out;
	cursor: pointer;
	float: left;
}

.wprmenu_icon.wprmenu_icon_par {
	top: 4px;
}

.wprmenu_icon span {
	display: block;
	position: absolute;
	height: 4px;
	background: #d3531a;
	border-radius: 0;
	opacity: 1;
	transform: rotate(0);
	transition: .25s ease-in-out;
}

.wprmenu_icon.open span:nth-child(1), .wprmenu_icon.open span:nth-child(4) {
	width: 0;
	left: 50%;
	top: 18px;
}


.wprmenu_icon span:nth-child(2), .wprmenu_icon span:nth-child(3) {
	top: 9px;
}

.wprmenu_icon span:nth-child(4) {
	top: 18px;
}

.wprmenu_icon.open span:nth-child(2) {
	transform: rotate(45deg);
}

.wprmenu_icon.open span:nth-child(3) {
	transform: rotate(-45deg);
}


#mg-wprm-wrap li.menu-item-has-children {
	position: relative;
}


#mg-wprm-wrap canvas {
	position: absolute;
	z-index: 99;
}

#mg-wprm-wrap div.wpr_search {
	padding: 13px;
}

#wprmenu_menu_ul {
	margin: 0;
	overflow: hidden;
}

#wprmenu_menu_ul ul.sub-menu {
	margin-left: 0;
}

#mg-wprm-wrap li.menu-item a {
	padding: 13px 14px;
}

#mg-wprm-wrap li.menu-item ul.sub-menu li.menu-item span {
	right: 10px;
	padding: 20px 20px 30px;
	line-height: 38px;
}

.wprmenu_clear {
	clear: both !important;
	display: block !important;
	height: 1px !important;
	margin: -1px 0 0 !important;
	width: 1px !important;
}

.wpr-clear {
	display: block;
}

.wpr-clear:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}

.wpr_search {
	padding-left: 0 !important;
	padding-right: 0 !important;
	margin-left: 8px !important;
	width: 92% !important;
}

#mg-wprm-wrap div.wpr_search {
	margin-top: 24px;
}

.cbp-spmenu {
	position: fixed;
	overflow: auto;
	height: 100%;
	z-index: 9999;
}

.cbp-spmenu a {
	padding: 1em;
	display: block;
}

.cbp-spmenu-left {
	left: -100%;
}

.cbp-spmenu-left.cbp-spmenu-open {
	left: 0;
}

.cbp-spmenu, .cbp-spmenu-push {
	transition: all .3s ease;
}

.wprmenu_icon span:nth-child(1), #wprmenu_bar, html body .wprm-overlay.active,
#mg-wprm-wrap.cbp-spmenu.custom {
	top: 0;
}

#wprmenu_bar, html body .wprm-overlay.active, .cbp-spmenu-push, .wprmenu_icon span {
	left: 0;
}


#wprmenu_menu_ul .sub-menu,
#wprmenu_menu_ul,
#wprmenu_bar,
#wprmenu_bar * {
	list-style: none;
}


/*
.cbp-spmenu-right {
	right: -100%;
}
.cbp-spmenu-right.cbp-spmenu-open {
	right: 0;
}
*/
/*
.cbp-spmenu-top {
	top: -100% !important;
}

.cbp-spmenu-bottom {
	top: 100% !important;
}
*/

/*
.cbp-spmenu-push {
	overflow-x: hidden;
	position: relative;
}

.cbp-spmenu-push-toright {
	left: 100%;
}

.cbp-spmenu-push-toleft {
	left: -100%;
}

.cbp-spmenu-push .cbp-spmenu-top {
	transition: unset;
}

.cbp-spmenu-push #mg-wprm-wrap.cbp-spmenu-top {
	position: static;
	width: 100%;
	max-width: 100%;
}

.cbp-spmenu-push .cbp-spmenu-top #wprmenu_menu_ul {
	padding: 0;
}
*/


/*
#wprmenu_bar,
#mg-wprm-wrap.custom .menu_title,
#mg-wprm-wrap,
.wpr_custom_menu #custom_menu_icon,
.cbp-spmenu-push #mg-wprm-wrap.cbp-spmenu-top {
	display: none;
}
*/



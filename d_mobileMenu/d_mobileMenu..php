<?php

$p = d_getPath(__FILE__);
$u = getUrlFromPath($p);
define('pathto_d_mobileMenu', $p);
define('urlto_d_mobileMenu', $u);

require_once 'd_mobileMenu_bar.php';
require_once 'd_mobileMenu_menu.php';

//add the css todo put this in scss
add_action('head_before_wp_head', function(){
	//echo CSSfile(urlto_d_mobileMenu.'css/wpr-hamburger.css', ['id' => 'wprm_hamburger']);
	// it goes theirs firsrt, then later style.css overrides
	echo CSSfile(urlto_d_mobileMenu.'css/wprmenu.css', ['id' => 'wprm_wprmenu']);
	echo CSSfile(urlto_d_mobileMenu.'css/wpr-icons.css', ['id' => 'wprm_wpr_icons']);
}, 1, 0);


// add the JS
add_action('head_after_wp_head', function(){
	?>
	<script type="text/javascript" id="wprmenu.js-js-extra">
		var wprmenu = {
			"zooming": ""
			, "from_width": "768"
			, "push_width": "400"
			, "menu_width": "80"
			, "parent_click": ""
			, "swipe": ""
			, "enable_overlay": ""
			, "wprmenuDemoId": ""
		};
	</script>

	<?php
	echo JSfile(urlto_d_mobileMenu.'js/modernizr.custom.js', ['id' => 'wprm_modernizr']);
	// i dont have swipe turned on so i dont need this
	// echo JSfile(urlto_d_mobileMenu.'js/touchSwipe.js', ['id' => 'wprm_touchSwipe']);
	echo JSfile(urlto_d_mobileMenu.'js/wprmenu.js', ['id' => 'wprm_wprmenu']);
}, 1, 0);


function d_mobileMenu(){
	d_mobileMenu_bar();
	d_mobileMenu_menu();
}

// todo clean this up
add_action('wp_footer', function(){
	echo comment('wp_footer action');
	$elements = ['nav li.menu-item-has-children > a[href="#"]',
				 'div.wprm-wrapper li.menu-item-has-children  a.wprmenu_parent_item[href="#"]',//old
				 'div.d_wprm-wrapper li.menu-item-has-children  a[href="#"]'];
	echo getHashtagIgnorer(implode(',', $elements));
}, 999, 0);


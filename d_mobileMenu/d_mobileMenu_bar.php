<?php

function d_mobileMenu_bar(){
	echo comment('start D mod menu bar in incs');

	$wpr_title      = apply_filters('wpr_title', getSiteTitle());
	$bar_logo       = apply_filters('bar_logo', '/_img/wprm-icon.png');
	$menuburgertext = apply_filters('menuburgertext', 'menu');
	$searchbox_text = apply_filters('searchbox_text', 'Search...');
	$searchbox      = apply_filters('searchbox', false);

	//keep the id for this div
	?>
	<div id="wprmenu_bar" class="d_mod  wprmenu_bar  d_flex_grid">
		<?php
		//<editor-fold desc="-- search box -- ">
		if($searchbox){ ?>
			<div class="toggle-search"><i class="<?=$searchbox_text?>"></i></div>
			<div class="search-expand">
				<div class="wpr_search">
					<?php echo wpr_search_form(); ?>
				</div>
			</div>
		<?php }
		//</editor-fold> -- search box --
		?>

		<div id="d_wprm_bar_logo">
			<a href="/">
				<img class="bar_logo" alt="logo" src="<?=$bar_logo?>">
			</a>
		</div>

		<div id="d_wprm_menu_title" class=" df_col">
			<span class="wpr_title"><?=$wpr_title?></span>
		</div>

		<div id="d_wprm_hamburger" class="hamburger hamburger--slider">
				<span class="hamburger-box">
				  <span class="hamburger-inner"></span>
				</span>
			<div id="menuburgertext"><?=$menuburgertext?></div>
		</div>

	</div>
	<?php
	echo comment('END D mod menu bar').nl().nl();
}


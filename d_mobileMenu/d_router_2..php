<?php
// you must define('d_router_2', true);
//load this last, so we have the path constants.
// see url and http.php function getRoute(){ which is just return noslasheither(stripslashes(removequeryString($_SERVER['REQUEST_URI'])));
$p = d_getPath(__FILE__);
$u = getUrlFromPath($p);
define('pathto_d_routes', $p);
define('urlto_d_routes', $u);

//$routes[] = ['route', 'filename.php', 'dir']
// where if filename.php contains a path  pathtothemeinc

// this action is after functions.php  so, this is the first action we can use (cuz we have routes in functions.php)
add_action('after_setup_theme', function(){
	$routes   = [];
	$routes[] = ['util', pathto_inc.'util.php'];
	$routes[] = ['_list_routes', pathto_d_routes.'_list_routes.php'];
	$routes[] = ['_dev_debug_auth', pathto_d_admin.'_dev_debug_auth.php', ['current_user_can' => 'read']];//$args['current_user_can']
	$routes[] = ['_test', pathto_d_routes.'_test.php', ['current_user_can' => 'read']];
	$routes   = apply_filters('d_router_2_routes', $routes);//get all the routes from other plugins and wp-config
	//	add_filter('d_router_2_routes', function($routes){
	// path is pathtothemeinc.$filename unless a path is included in $filename
	//		$routes[] = ['_test', pathto_d_routes.'_test.php', ['current_user_can' => 'read']];
	//		$routes[] = ['scadd', 'scadd.php'];//, ['current_user_can' => 'edit_others_posts']
	//		return $routes;
	//	}, 99, 1);
	//
	new d_router_2($routes);
});

/*
With this routing setup, WordPress doesn't attempt to query or load posts for routes that you have defined and intercepted with your custom logic. You are essentially telling WordPress that for these specific routes, you'll handle everything – so it doesn't proceed with its usual operations.
d_router_2, the routes will now be formed like
$routes[] = ['route', 'filename or URL', $args]

2nd arg is filename or URL or function.
2nd arg not present: it is assumed to be 'route'.'.php'
  	the filename will be pathtothemeinc.filename.ext.
	if there is a path it will be pathtothemeinc.path/to/filename.ext
2nd arg starts with // or http, it's an url
	// is a relative url to this site
	http is an absolute url
2nd arg ends with () it's a function name.
	it will be called with no args (but you could use globals)

3rd arg is optional, and is an associative array.
	'current_user_can', this is to be used with the function current_user_can(value) to determine if the user has access.
	'role' checks heirarchy of 0 	administrator or admin,  1 	editor,  2 	author,  3 	contributor,  4 	subscriber,  1000 guest
	must have one of these or it will default to role = admin
	if it's not an array, but a string, it will be defaulted to 'role' => 'admin'
	you can put other args in there, but they are not used by the router.  There might be other uses.

$routes[] = ['route', 'filename.php', ['current_user_can' => 'read']];// pathtothemeinc.dir.'/'.filename.php  this is public.
$routes[] = ['route1', 'filename.php', ['role' => 'contributor']];    // must be an contributor or above
$routes[] = ['route2', sdr.'sdr_to/path/filename.php'];        // /server/document/root/to/filename.php   contains sdr

$routes[] = ['route3', 'filename.php'];                    // pathtothemeinc.filename.php
$routes[] = ['route4', 'path/to/mydir/filename.php'];    // pathtothemeinc.'path/to/mydir/filename.php
$routes[] = ['route5'];                                // pathtothemeinc.'route5.php

$routes[] = ['route6', 'http://example.com/filename.php'];    // http://example.com/filename.php
$routes[] = ['route7', '//relative/url/to_somewhere'];        // http://thisdomain.com/relative/url/to_somewhere . 'some/url/with/params/', ['current_user_can' => 'read']];
$routes[] = ['route71_dbouleshash', '//'];                    // http://thisdomain.com/   redirect to url root.

$routes[] = ['routeandrole', ['role' => 'admin']];
$routes[] = ['routeFilerole', 'myfile.php', ['role' => 'contributor']];
$routes[] = ['routeUrlrole', '//myurl/path', ['role' => 'contributor']];
*/

class d_router_2{
	protected $routes = [];

	public function __construct($routes){
		$this->routes                 = $routes;
		$GLOBALS['d_router_2_routes'] = $this->routes;

		add_action('parse_request', [$this, 'handleRequest'], 1);         // High priority
		d_flush_rewrite('d_router_2_flusher', json_encode($this->routes));// randomDigitNumber(12));     //
	}

	public function handleRequest(){
		$GLOBALS['d_router_2_routename'] = false;
		$requested_path                  = noslasheither(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));  // '/'.trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');//gets the route from the url.  like /myroute
		//d($requested_path, '$requested_path');
		//ddie($requested_path, '$requested_path');
		foreach($this->routes as $route){
			//d($route, '$route');
			$routename = noslasheither($route[0]);//d($routename, '$routename');
			if($routename === $requested_path){//match one of my routes up with the route from the url
				$r                               = $this->normalizeRoute($route);//d($r, '$r');
				$GLOBALS['d_router_2_routename'] = $routename;
				//d($GLOBALS['d_router_2_routename'], '$GLOBALS[d_router_2_routename]');
				if($r['url']){
					$GLOBALS['d_router_2_url'] = $r['url'];
				}else{
					$GLOBALS['d_router_2_filename'] = basename($r['filename']);
					$GLOBALS['d_router_2_filepath'] = $r['filename'];
				}

				// Check user access before loading route
				if($this->userHasAccess($r['args'])){
					if($r['url']){
						redirect301($r['url']);
					}

					if($r['func']){
						$f = str_replace('()', '', $r['func']);
						if(function_exists($f)){
							call_user_func($f);
						}
						exit;
					}

					$this->loadTemplate($r['filename']);//include it or 404
					exit;
				}else{
					$rt         = "/{$requested_path}";
					$return_url = add_query_arg($_GET, $rt);
					wp_redirect(wp_login_url($return_url));
					exit;
				}
			}
		}
	}
	//0 	administrator or admin
	//1 	editor
	//2 	author
	//3 	contributor
	//4 	subscriber
	//public posts, you have to  explicitly set `role' => 'guest'`
	private function userHasAccess($args = []){
		if( !empty($args['current_user_can'])){// use this for special stuff
			return current_user_can($args['current_user_can']);
		}

		// Check for 'role' argument in addition to 'current_user_can'
		$role = $args['role'] ?? 'administrator'; // Default to 'administrator'  or admin works.
		return user_has_at_least_role($role);
	}


	protected function loadTemplate($templateFile){
		//d($templateFile, '$templateFile');
		if(file_exists($templateFile)){ // Use $templateFile correctly
			include $templateFile;
			return;
		}

		status_header(404); // Set status header
		nocache_headers();
		include get_404_template(); // Load the default 404 template
	}

	public function normalizeRoute($route){
		unset($r);
		$r['route'] = noslasheither($route[0]);
		if(is_array($route[1])){
			$r['filename'] = $route[0].'.php';
			$r['args']     = $route[1];
		}else{
			$r['filename'] = $route[1] ? : $route[0].'.php';
			$args          = $route[2];
			if( !is_array($args) and !empty($args) and is_string($args)){
				$r['args'] = ['role' => $args];
			}elseif( !empty($args)){
				$r['args'] = $args;
			}
		}
		// check $r['args']  it needs to have $r['args']['role'] or $r['args']['current_user_can'] and if not, $r['args']['role'] = 'admin'  also, they cant be empty string
		if( //
			( !isset($r['args']['role']) && !isset($r['args']['current_user_can'])) or //
			(isset($r['args']['role']) && empty($r['args']['role'])) //
		){
			$r['args']['role'] = 'admin';
		}


		// dboule slash or http[s] for a url.
		if(startsWith($r['filename'], '//') or istartsWith($r['filename'], 'http')){
			$r['url'] = getAbsoluteURL($r['filename']);
			unset($r['filename']);
		}
		if(endsWith($r['filename'], '()')){
			$r['func'] = $r['filename'];
			unset($r['filename']);
		}
		if($r['filename']){
			if( !ihaystackcontainsneedle($r['filename'], sdr)){// If filename contains a  full path
				$r['filename'] = pathtothemeinc.$r['filename'];
			}
		}

		return $r;
	}


}







<?=doctypeHTMLtag(' id="'.childtheme.'" ')?>
	<head>
		<title>
			<?php
			global $post, $d_post;
			//	getSiteDesc()  these are good 2025
			//	getSiteName()
			add_filter('wp_title', function($title){
				if(is_home() || is_front_page() or empty($title) or is_404()){
					$title = getSiteName();
				}
				return $title;
			});
			wp_title(''); ?></title>
		<?php
		echo boringMetaTags().faviconTags();

		// google fonts
		get_template_part('parts/head_before_wp_head');
		do_action('head_before_wp_head');

		echo comment(' START wp_head() from   '.filename(__FILE__).' '.__LINE__);
		wp_head();
		echo comment(' END wp_head() from   '.filename(__FILE__).' '.__LINE__);

		// the main style.css
		get_template_part('parts/head_after_wp_head');
		do_action('head_after_wp_head');

		get_template_part('parts/head_last');//, null, ['d_post' => $d_post]
		?>
	</head>


<?=comment('start body ---------------------------------------')?>


<body <?php body_class(); ?> >
<?php
$headers_container_class = apply_filters('headers_container_class', []);
$headers_container_class = implode(' ', $headers_container_class);
?>
	<div id="headers_container" class="<?=$headers_container_class?>">
		<div id="mainheader_container" class="">
			<div id="mainheader_wrapper" class="cf">
				<div id="desktop_menu">
					<div id="logo-and-text">
						<?php get_template_part('parts/header_logo') ?>
						<div id="text">
							<?php
							$headertitle = apply_filters('d_headertitle', getSiteTitle());
							$headerdesc  = apply_filters('d_headerdesc', getSiteDesc());
							?>
							<div id="site-title-text"><a href="/"><?=$headertitle?></a></div>
							<div id="site-description-text"><?=$headerdesc?></div>
						</div>
					</div>
					<div id="menu">
						<?php
						get_template_part('parts/header_main_menu');
						?>
					</div>
				</div>
			</div><!-- mainheader_wrapper -->
			<div id="mobile_main_header_wrapper">
				<div id="mobile_menu">
					<?php
					get_template_part('parts/header_mobile_menu');
					?>
				</div>
			</div>
		</div><!-- end #mainheader_container -->
	</div><!-- end #headers_container -->
<?php
//get_template_part('parts/headersecondary'); not used. kinda fucky because of wpr menu.
// we need to redo the menu and make it part of the flow instead of abso positioned.
?>

<div id="body_container" class="container ">
<?php
get_template_part('parts/in_body_container');
?>
<div id="body_wrapper" class="content wrapper <?=$GLOBALS['bodywrapper_more_class'] ?? ''?> ">
<?php
get_template_part('parts/in_body_wrapper');

do_action('start_body_wrapper');//mostly just for 404, as of yet.

echo comment('ENDDDDDDDDDDDDDDD '.filename(__FILE__).' '.__LINE__.' start the template');


<?php

echo comment('');
//if we are in a child theme, use the child theme style.css
//else, use the parent theme style.css

// still need this if we have source maps. the maps dont work good on the compressed.  Also, dont need peekers peaking.
$fn = (is_dev()) ? 'style.debug.css' : 'style.css';


if(is_child_theme()){
	$styleurl = urltotheme.$fn;//use the child theme style.css
	$id       = childtheme;
}else{
	$styleurl = urltothemeparent.$fn;//use the parent theme style.css, this theme.
	$id       = parenttheme.'-parent';
}

$styleurl   = urlReplaceParams(['version' => get_bloginfo('version')], $styleurl);
$stylesheet = '<link rel="stylesheet" id="'.$id.'-style-css" href="'.$styleurl.'" type="text/css" media="all">';
echo $stylesheet;

?>


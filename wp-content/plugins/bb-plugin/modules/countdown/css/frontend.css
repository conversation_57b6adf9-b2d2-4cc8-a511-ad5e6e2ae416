.fl-countdown{
	text-align: center;
	font-size: 1px;
}
.fl-countdown .fl-countdown-unit-number {
	display: block;
}
.fl-countdown .fl-countdown-number{
	position: relative;
	display: inline-block;
}

.fl-countdown .fl-countdown-unit{
	position: relative;
}
.fl-countdown .fl-countdown-unit-label {
	min-width: 80px;
}

.fl-countdown.fl-countdown-separator-colon .fl-countdown-number:after{
	content: ':';
	display: inline-block;
	font-size: 30px;
	position: absolute;
	right: 0;
	top: 50%;
	z-index: 10;
	-webkit-transform: translateY(-50%);
	   -moz-transform: translateY(-50%);
			transform: translateY(-50%);
}
.fl-countdown.fl-countdown-separator-line .fl-countdown-number:after{
	content: '';
	display: inline-block;
	position: absolute;
	top: 0;
	bottom: 0;
	z-index: 10;
	border-right: 1px solid #ccc;
}
.fl-countdown.fl-countdown-separator-colon .fl-countdown-number:last-child:after,
.fl-countdown.fl-countdown-separator-line .fl-countdown-number:last-child:after{
	content: none;
}

.fl-countdown .svg-container{
	display: inline-block;
	position: relative;
	z-index: 1;
	width: 100%;
	height: auto;
	padding-bottom: 100%;
}

.fl-countdown .svg-container .svg{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
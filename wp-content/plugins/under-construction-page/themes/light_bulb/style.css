/*
 * UnderConstructionPage
 * Light Bulb theme CSS
 * (c) WebFactory Ltd, 2015 - 2023
 */


html {
   height: 100%;
   padding: 0;
   margin: 0;
}

body {
  font-weight: 400;
  font-size: 14px;
  line-height: 120%;
  color:  #2e2e2e; /* #b9b9b9; */


background: #8e9eab;  /* fallback for old browsers */
background: -webkit-linear-gradient(to right, #eef2f3, #8e9eab);  /* Chrome 10-25, Safari 5.1-6 */
background: linear-gradient(to right, #eef2f3, #8e9eab); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */


  padding: 0;
  margin: 0;
  height: 100%;
}

.top-container {
  padding: 50px 0;
}

#hero-image {
  text-align: center;
}

#hero-image img {
  max-width: 80%;
}

.display-table {
  display: table;
  table-layout: fixed;
}

.display-cell {
  display: table-cell;
  vertical-align: middle;
  float: none;
}

h1 {
  font-size: 34px;
  color: #2e2e2e;
  font-family: "Nunito", sans-serif;
  font-weight: 900;
  margin: 20px 0 30px 0;
  text-align: right;
}

.content {
  text-align: center;
  font-family: "Helvetica", "Arial", sans-serif;
}

#social {
  text-align: center;
  margin-top: 30px;
}

#social a i {
  color: #414042;
  margin: 10px;
  box-sizing: content-box;
}

#social a:hover i {
  color: #f9b91a;
}

@media(max-width:767px) {
  h1 {
     font-size: 28px;
     margin: 10px 0 30px 0;
  }
  #hero-image img {
    max-width: 95%;
  }
}

@media (max-width: 1200px) {
  h1 {
    text-align: center;
    padding: 0 20px;
  }
  .display-table {
    display: initial;
    table-layout: auto;
  }
  .display-cell {
    display: block;
    vertical-align: middle;
    float: none;
  }
  #hero-image img {
    max-height: 250px
  }
  .top-container {
    padding: 25px 0;
  }
}

@charset "UTF-8";
/*!  This (!) needed to keep the comment in there, for WP
	Template:  d_theme_3
	Theme Name:   d_theme_3_child
*/
@media screen and (max-width: 768px) {
  .hide_up_to_ipad, .hide768, .hideIpad {
    display: none;
  }
}

@media screen and (min-width: 769px) {
  .hide_more_than_ipad, .show768, .hideLarge {
    display: none;
  }
}

@media screen and (min-width: 1151px) {
  .hide_more_than_wrapper, .hide-above-wrapper {
    display: none;
  }
}

@media screen and (max-width: 1150px) {
  .hide_up_to_wrapper, .show-above-wrapper {
    display: none;
  }
}

html {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 !important;
  margin: 0 !important;
}

*, *:before, *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.displaynone {
  display: none;
}

.displaynoneimportant {
  display: none !important;
}

table td {
  vertical-align: top;
}

.clearfix, .cf {
  clear: both;
}
.clearfix:after, .cf:after {
  content: "";
  display: table;
  clear: both;
}

.redux-container, .redux-container *,
#redux-sub-footer, #redux-sub-footer * {
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

a, a:focus, a:active, a:hover, object, embed, input::-moz-focus-inner {
  outline: 0;
  background: transparent;
}

img {
  border: none;
  max-width: 100%;
  height: auto;
  vertical-align: bottom;
}

.d-vertical-center-transform-outer {
  height: 100%;
  position: relative;
}
.d-vertical-center-transform-outer .d-vertical-center-transform-inner {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.d-vertical-center-table-outer {
  display: table;
  height: 100%;
  background: green;
}
.d-vertical-center-table-outer .d-vertical-center-table-inner {
  display: table-cell;
  vertical-align: middle;
  background: pink;
}

.d-vertical-center-flex-outer-container {
  display: flex;
}
.d-vertical-center-flex-outer-container .d-vertical-center-flex-outer {
  display: flex;
  align-items: center;
  justify-content: center;
}
html {
  font-family: "Inconsolata", "Arial", "Helvetica", "Liberation Sans", Tahoma, Geneva, sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  font-size: 16px;
}

body {
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #fff;
  background-image: none;
  background-size: cover;
  background-attachment: fixed;
  font-family: "Inconsolata", "Arial", "Helvetica", "Liberation Sans", Tahoma, Geneva, sans-serif;
  line-height: 1.5;
  color: #273C3A;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
  display: block;
}

audio, canvas, video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden], template {
  display: none;
}

u {
  text-decoration: underline;
}

a {
  background: transparent;
  background-color: transparent;
}

a:focus, a:active, a:hover {
  outline: 0;
}

b, strong, .strong {
  font-weight: bold;
}

dfn, em, .em {
  font-style: italic;
}

abbr[title] {
  border-bottom: 1px dotted;
}

figure {
  margin: 0;
}

hr {
  height: 0;
  margin: 1% 0;
}

hr.solid {
  border: none !important;
  color: #000;
  background: #000;
}

p {
  word-break: normal; /* Let the browser handle word wrapping */
  overflow-wrap: break-word; /* Ensures long words wrap properly */
  hyphens: auto; /* Adds hyphens where appropriate */
}

code, kbd, pre, samp {
  font-family: monospace, sans-serif;
  font-size: 1em;
}

pre {
  white-space: pre-wrap;
}

q {
  quotes: "“" "”" "‘" "’";
}

q:before, q:after {
  content: "";
  content: none;
}

small, .small {
  font-size: 75%;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

ol, ul {
  padding: 0 0 0 40px;
}

ul {
  list-style: disc;
  list-style-type: disc;
}
ul ul {
  list-style: circle;
  list-style-type: circle;
}

ul.unstyled, ol.unstyled,
nav ul, nav ol,
.nav ul, .nav ol {
  list-style: none;
  list-style-image: none;
  padding: 0;
  margin: 0;
}

svg:not(:root) {
  overflow: hidden;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
}

button, input, select, textarea {
  font-family: inherit;
  font-size: 100%;
  margin: 0;
}

button, input {
  line-height: normal;
}

button, select {
  text-transform: none;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
  cursor: pointer;
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
button:focus:focus-visible,
[type=button]:focus:focus-visible,
[type=reset]:focus:focus-visible,
[type=submit]:focus:focus-visible {
  outline: 2px solid Highlight;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: 2px;
}
button:focus:focus:not(:focus-visible),
[type=button]:focus:focus:not(:focus-visible),
[type=reset]:focus:focus:not(:focus-visible),
[type=submit]:focus:focus:not(:focus-visible) {
  outline: none;
}
button:disabled,
[type=button]:disabled,
[type=reset]:disabled,
[type=submit]:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

input[type=checkbox], input[type=radio] {
  padding: 0;
}

input[type=search] {
  -webkit-appearance: textfield;
}

input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
  vertical-align: top;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

.d_flex_grid {
  /* https://css-tricks.com/using-flexbox/ : */
  display: -webkit-box; /* OLD - iOS 6-, Safari 3.1-6 */
  display: -moz-box; /* OLD - Firefox 19- (buggy but mostly works) */
  display: -ms-flexbox; /* TWEENER - IE 10 */
  display: -webkit-flex; /* NEW - Chrome */
  display: flex; /* NEW, Spec - Opera 12.1, Firefox 20+ */
  max-width: 100%;
}

.centering_grid {
  display: -webkit-box; /* OLD - iOS 6-, Safari 3.1-6 */
  display: -moz-box; /* OLD - Firefox 19- (buggy but mostly works) */
  display: -ms-flexbox; /* TWEENER - IE 10 */
  display: -webkit-flex; /* NEW - Chrome */
  display: flex; /* NEW, Spec - Opera 12.1, Firefox 20+ */
  flex-direction: column; /* make main axis vertical */
  justify-content: center; /* center items vertically, in this case */
  align-items: center; /* center items horizontally, in this case */
}

.horizcenter {
  justify-content: center;
  align-items: center;
}

.vertcenter {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.d_flex_grid .df_col {
  -webkit-box-flex: 1 1 0px; /* x OLD - iOS 6-, Safari 3.1-6 */ /* needs to have the 0px on the end.  0 dont work, for IE. */
  -moz-box-flex: 1 1 0px; /* OLD - Firefox 19- */
  -webkit-flex: 1 1 0px; /* Chrome */
  -ms-flex: 1 1 0px; /* IE 10 */
  flex: 1 1 0px; /* NEW, Spec - Opera 12.1, Firefox 20+ */
  padding-right: 2%;
  flex-basis: auto;
}

.d_flex_grid .shrinktofit {
  flex: 0 0 0;
}

.d_flex_grid .df_col.halfof3 {
  flex: 0 0 50%; /* have to account for margins i guess */
  width: 50%;
}

.d_flex_grid .df_col.sidebar320 {
  flex: 0 0 320px;
}

.d_flex_grid .df_col.sidebar200 {
  flex: 0 0 200px;
}

.d_flex_grid.two_one {
  flex-wrap: wrap;
  justify-content: flex-start;
}
.d_flex_grid.two_one .df_col {
  width: 49%;
  max-width: 49%;
}
.d_flex_grid.two_one .df_col:nth-child(2n+0) {
  padding-right: 0;
}

@media (max-width: 768px) {
  .d_flex_grid.two_one .df_col {
    width: 100%;
    max-width: 100%;
    margin-bottom: 2vw;
    padding-right: 2%;
  }
}
.d_flex_grid.five_three_two {
  flex-wrap: wrap;
  justify-content: flex-start;
}
.d_flex_grid.five_three_two .df_col {
  width: 20%;
  max-width: 20%;
  margin-bottom: 2vw;
}
.d_flex_grid.five_three_two .df_col:nth-child(5n+0) {
  padding-right: 0;
}

@media (max-width: 768px) {
  .d_flex_grid.five_three_two .df_col {
    width: 32%;
    max-width: 32%;
    margin-bottom: 2vw;
  }
  .d_flex_grid.five_three_two .df_col:nth-child(5n+0) {
    padding-right: 2%;
  }
  .d_flex_grid.five_three_two .df_col:nth-child(3n+0) {
    padding-right: 0%;
  }
}
@media (max-width: 500px) {
  .d_flex_grid.five_three_two .df_col {
    width: 50%;
    max-width: 50%;
    margin-bottom: 2vw;
  }
  .d_flex_grid.five_three_two .df_col:nth-child(5n+0) {
    padding-right: 0%;
  }
  .d_flex_grid.five_three_two .df_col:nth-child(3n+0) {
    padding-right: 0%;
  }
  .d_flex_grid.five_three_two .df_col:nth-child(1n+0) {
    padding-right: 2%;
  }
  .d_flex_grid.five_three_two .df_col:nth-child(2n+0) {
    padding-right: 0%;
  }
}
.d_flex_grid.four_two_one {
  width: 25%;
  max-width: 25%;
  flex-wrap: wrap;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .d_flex_grid.four_two_one .df_col {
    flex: 1 1 48%;
    margin-bottom: 2vw;
    padding-right: 0 !important;
  }
  .d_flex_grid.four_two_one .df_col:nth-child(1),
  .d_flex_grid.four_two_one .df_col:nth-child(3) {
    padding-right: 2%;
  }
}
@media (max-width: 600px) {
  .d_flex_grid.four_two_one {
    display: block;
  }
  .d_flex_grid.four_two_one .df_col {
    padding-right: 0;
    margin-bottom: 2%;
  }
}
/* */
a {
  color: #273C3A;
  text-decoration: none;
}
a:link, a:visited {
  color: #273C3A;
  text-decoration: none;
}
a:hover {
  color: #187177;
  text-decoration: underline;
}
a:focus, a:active {
  color: #273C3A;
}

a:link {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.3);
}

a.underline_no_yes, a.underline_no_yes:link, a.underline_no_yes:visited, a.underline_no_yes:active {
  text-decoration: none;
}
a.underline_no_yes:focus, a.underline_no_yes:hover {
  text-decoration: underline;
}

a.underline_no_no, a.underline_no_no:link, a.underline_no_no:visited, a.underline_no_no:active {
  text-decoration: none;
}
a.underline_no_no:focus, a.underline_no_no:hover {
  text-decoration: none;
}

/* xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx   */
.capitalized {
  text-transform: capitalize;
}

.alignleft {
  text-align: left;
}

.aligncenter {
  text-align: center;
}

.alignright {
  text-align: right;
}

.darkbg {
  background-color: #000000;
  color: #ECDCD3;
}
.darkbg a {
  color: #ECDCD3 !important;
  text-decoration: none;
}
.darkbg a:link, .darkbg a:visited {
  color: #ECDCD3 !important;
  text-decoration: none;
}
.darkbg a:hover {
  color: #ddd !important;
  text-decoration: underline;
}
.darkbg a:focus, .darkbg a:active {
  color: #ECDCD3 !important;
}

#content p, .content p {
  margin: 0 0 1.5em;
}
#content p a, .content p a {
  text-decoration: underline;
}
#content p a:visited, .content p a:visited {
  text-decoration: underline;
}
#content p a:hover, #content p a:focus, #content p a:active, .content p a:hover, .content p a:focus, .content p a:active {
  text-decoration: none;
}
#content img.alignleft, .content img.alignleft {
  margin-right: 1.5em;
  display: inline;
  float: left;
}
#content img.alignright, .content img.alignright {
  margin-left: 1.5em;
  display: inline;
  float: right;
}
#content img.aligncenter, .content img.aligncenter {
  margin-right: auto;
  margin-left: auto;
  display: block;
  clear: both;
}
@media screen and (max-width: 1000px) {
  #content img.alignleft,
  #content img.alignright,
  #content img.aligncenter, .content img.alignleft,
  .content img.alignright,
  .content img.aligncenter {
    max-width: 75%;
  }
}
@media screen and (max-width: 768px) {
  #content img.alignleft,
  #content img.alignright,
  #content img.aligncenter, .content img.alignleft,
  .content img.alignright,
  .content img.aligncenter {
    max-width: 50%;
  }
}
@media screen and (max-width: 600px) {
  #content img.alignleft,
  #content img.alignright,
  #content img.aligncenter, .content img.alignleft,
  .content img.alignright,
  .content img.aligncenter {
    max-width: 100%;
    float: none;
    margin: 0;
    display: block;
    width: 100%;
  }
}
#content blockquote, .content blockquote {
  margin: 0 0 1.5em 0.75em;
  padding: 0 0 0 0.75em;
  border-left: 3px solid #187177;
  font-style: italic;
  color: #999;
}
#content dl, #content dt, #content dd, .content dl, .content dt, .content dd {
  margin-left: 0;
  font-size: 0.9em;
  color: #787878;
  margin-bottom: 1.5em;
}

.dashicons, .dashicons-before::before, .dashicons-before::after, a:has(.dashicons) {
  text-decoration: none !important;
  text-decoration-color: #fff;
  color: #273C3A;
  text-decoration: none;
}
.dashicons a, .dashicons-before::before a, .dashicons-before::after a, a:has(.dashicons) a {
  color: #273C3A;
  text-decoration: none;
}
.dashicons a:link, .dashicons a:visited, .dashicons-before::before a:link, .dashicons-before::before a:visited, .dashicons-before::after a:link, .dashicons-before::after a:visited, a:has(.dashicons) a:link, a:has(.dashicons) a:visited {
  color: #273C3A;
  text-decoration: none;
}
.dashicons a:hover, .dashicons-before::before a:hover, .dashicons-before::after a:hover, a:has(.dashicons) a:hover {
  color: #187177 !important;
  text-decoration: none;
}
.dashicons a:focus, .dashicons a:active, .dashicons-before::before a:focus, .dashicons-before::before a:active, .dashicons-before::after a:focus, .dashicons-before::after a:active, a:has(.dashicons) a:focus, a:has(.dashicons) a:active {
  color: #273C3A;
}
.dashicons:link, .dashicons:visited, .dashicons-before::before:link, .dashicons-before::before:visited, .dashicons-before::after:link, .dashicons-before::after:visited, a:has(.dashicons):link, a:has(.dashicons):visited {
  color: #273C3A;
  text-decoration: none;
}
.dashicons:hover, .dashicons-before::before:hover, .dashicons-before::after:hover, a:has(.dashicons):hover {
  color: #187177;
  text-decoration: none;
}
.dashicons:focus, .dashicons:active, .dashicons-before::before:focus, .dashicons-before::before:active, .dashicons-before::after:focus, .dashicons-before::after:active, a:has(.dashicons):focus, a:has(.dashicons):active {
  color: #273C3A;
}

.byline {
  color: #ddd;
  font-style: italic;
  margin: 0;
}

.wp-caption {
  max-width: 100%;
  background: #eee;
  padding: 5px;
}
.wp-caption img {
  max-width: 100%;
  margin-bottom: 0;
  width: 100%;
}
.wp-caption p.wp-caption-text {
  font-size: 0.85em;
  margin: 4px 0 7px;
  text-align: center;
}

#comments-title {
  padding: 0.75em;
  margin: 0;
  border-top: 1px solid #ddd;
}

.commentlist {
  margin: 0;
  list-style-type: none;
}

.comment {
  position: relative;
  clear: both;
  overflow: hidden;
  padding: 1.5em;
  border-bottom: 1px solid #ddd;
}
.comment .comment-author {
  padding: 7px;
  border: 0;
}
.comment .vcard {
  margin-left: 50px;
}
.comment .vcard cite.fn {
  font-weight: 700;
  font-style: normal;
}
.comment .vcard time {
  display: block;
  font-size: 0.9em;
  font-style: italic;
}
.comment .vcard time a {
  color: #403f3f;
  text-decoration: none;
}
.comment .vcard time a:hover {
  text-decoration: underline;
}
.comment .vcard .avatar {
  position: absolute;
  left: 16px;
  border-radius: 50%;
}
.comment:last-child {
  margin-bottom: 0;
}
.comment .children {
  margin: 0;
}
.comment[class*=depth-] {
  margin-top: 1.1em;
}
.comment.depth-1 {
  margin-left: 0;
  margin-top: 0;
}
.comment:not(.depth-1) {
  margin-top: 0;
  margin-left: 7px;
  padding: 7px;
}
.comment.odd {
  background-color: #fff;
}
.comment.even {
  background: #ddd;
}

.comment_content p {
  margin: 0.7335em 0 1.5em;
  font-size: 1em;
  line-height: 1.5em;
}

.comment-reply-link {
  font-size: 0.9em;
  float: right;
}
.comment-edit-link {
  font-style: italic;
  margin: 0 7px;
  text-decoration: none;
  font-size: 0.9em;
}

.comment-respond {
  padding: 1.5em;
  border-top: 1px solid #ddd;
}
.comment-respond #reply-title {
  margin: 0;
}
.comment-respond .logged-in-as {
  color: #403f3f;
  font-style: italic;
  margin: 0;
}
.comment-respond .logged-in-as a {
  color: #403f3f;
}
.comment-respond .comment-form-comment {
  margin: 1.5em 0 0.75em;
}
.comment-respond .form-allowed-tags {
  padding: 1.5em;
  background-color: #ddd;
  font-size: 0.9em;
}
.comment-respond #submit {
  float: right;
  font-size: 1em;
}
.comment-respond #comment-form-title {
  margin: 0 0 1.1em;
}
#allowed_tags {
  margin: 1.5em 10px 0.7335em 0;
}

.nocomments {
  margin: 0 20px 1.1em;
}

input[type=text],
input[type=password],
input[type=datetime],
input[type=datetime-local],
input[type=date],
input[type=month],
input[type=time],
input[type=week],
input[type=number],
input[type=email],
input[type=url],
input[type=search],
input[type=tel],
input[type=color],
input[type=submit],
button,
select,
textarea,
.field {
  color: #403f3f;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: none;
  display: block;
  padding: 0 0 0 12px;
  margin-bottom: 1vw;
  font-size: 1em;
  vertical-align: middle;
  height: 40px;
  line-height: 1;
  width: 100%;
  max-width: 400px;
  font-family: Consolas, "Liberation Mono", "Courier New", "Lucida Console", Courier, monospace;
  transition: background-color 0.24s ease-in-out;
}
input[type=text]:focus, input[type=text]:active,
input[type=password]:focus,
input[type=password]:active,
input[type=datetime]:focus,
input[type=datetime]:active,
input[type=datetime-local]:focus,
input[type=datetime-local]:active,
input[type=date]:focus,
input[type=date]:active,
input[type=month]:focus,
input[type=month]:active,
input[type=time]:focus,
input[type=time]:active,
input[type=week]:focus,
input[type=week]:active,
input[type=number]:focus,
input[type=number]:active,
input[type=email]:focus,
input[type=email]:active,
input[type=url]:focus,
input[type=url]:active,
input[type=search]:focus,
input[type=search]:active,
input[type=tel]:focus,
input[type=tel]:active,
input[type=color]:focus,
input[type=color]:active,
input[type=submit]:focus,
input[type=submit]:active,
button:focus,
button:active,
select:focus,
select:active,
textarea:focus,
textarea:active,
.field:focus,
.field:active {
  background-color: #efefef;
  outline: none;
  border: 1px solid #bbb !important;
}
input[type=text][disabled], input[type=text].is-disabled,
input[type=password][disabled],
input[type=password].is-disabled,
input[type=datetime][disabled],
input[type=datetime].is-disabled,
input[type=datetime-local][disabled],
input[type=datetime-local].is-disabled,
input[type=date][disabled],
input[type=date].is-disabled,
input[type=month][disabled],
input[type=month].is-disabled,
input[type=time][disabled],
input[type=time].is-disabled,
input[type=week][disabled],
input[type=week].is-disabled,
input[type=number][disabled],
input[type=number].is-disabled,
input[type=email][disabled],
input[type=email].is-disabled,
input[type=url][disabled],
input[type=url].is-disabled,
input[type=search][disabled],
input[type=search].is-disabled,
input[type=tel][disabled],
input[type=tel].is-disabled,
input[type=color][disabled],
input[type=color].is-disabled,
input[type=submit][disabled],
input[type=submit].is-disabled,
button[disabled],
button.is-disabled,
select[disabled],
select.is-disabled,
textarea[disabled],
textarea.is-disabled,
.field[disabled],
.field.is-disabled {
  cursor: not-allowed;
  border-color: #ccc;
  opacity: 0.6;
}
input[type=text][disabled]:focus, input[type=text][disabled]:active, input[type=text].is-disabled:focus, input[type=text].is-disabled:active,
input[type=password][disabled]:focus,
input[type=password][disabled]:active,
input[type=password].is-disabled:focus,
input[type=password].is-disabled:active,
input[type=datetime][disabled]:focus,
input[type=datetime][disabled]:active,
input[type=datetime].is-disabled:focus,
input[type=datetime].is-disabled:active,
input[type=datetime-local][disabled]:focus,
input[type=datetime-local][disabled]:active,
input[type=datetime-local].is-disabled:focus,
input[type=datetime-local].is-disabled:active,
input[type=date][disabled]:focus,
input[type=date][disabled]:active,
input[type=date].is-disabled:focus,
input[type=date].is-disabled:active,
input[type=month][disabled]:focus,
input[type=month][disabled]:active,
input[type=month].is-disabled:focus,
input[type=month].is-disabled:active,
input[type=time][disabled]:focus,
input[type=time][disabled]:active,
input[type=time].is-disabled:focus,
input[type=time].is-disabled:active,
input[type=week][disabled]:focus,
input[type=week][disabled]:active,
input[type=week].is-disabled:focus,
input[type=week].is-disabled:active,
input[type=number][disabled]:focus,
input[type=number][disabled]:active,
input[type=number].is-disabled:focus,
input[type=number].is-disabled:active,
input[type=email][disabled]:focus,
input[type=email][disabled]:active,
input[type=email].is-disabled:focus,
input[type=email].is-disabled:active,
input[type=url][disabled]:focus,
input[type=url][disabled]:active,
input[type=url].is-disabled:focus,
input[type=url].is-disabled:active,
input[type=search][disabled]:focus,
input[type=search][disabled]:active,
input[type=search].is-disabled:focus,
input[type=search].is-disabled:active,
input[type=tel][disabled]:focus,
input[type=tel][disabled]:active,
input[type=tel].is-disabled:focus,
input[type=tel].is-disabled:active,
input[type=color][disabled]:focus,
input[type=color][disabled]:active,
input[type=color].is-disabled:focus,
input[type=color].is-disabled:active,
input[type=submit][disabled]:focus,
input[type=submit][disabled]:active,
input[type=submit].is-disabled:focus,
input[type=submit].is-disabled:active,
button[disabled]:focus,
button[disabled]:active,
button.is-disabled:focus,
button.is-disabled:active,
select[disabled]:focus,
select[disabled]:active,
select.is-disabled:focus,
select.is-disabled:active,
textarea[disabled]:focus,
textarea[disabled]:active,
textarea.is-disabled:focus,
textarea.is-disabled:active,
.field[disabled]:focus,
.field[disabled]:active,
.field.is-disabled:focus,
.field.is-disabled:active {
  background-color: #ccc;
}

button, input[type=submit], label.fileButton, .button {
  font-family: inherit;
  font-weight: bold;
  height: auto;
  padding: 5px 13px 6px 12px;
  border-color: #aaa;
  border: 1px solid;
  border-radius: 3px;
  background: #fff;
  margin: 0;
  max-width: fit-content;
  display: inline-block;
  cursor: pointer;
}
.powerpress_player button, .powerpress_player input[type=submit], .powerpress_player label.fileButton, .powerpress_player .button {
  max-width: none;
}

.formFileInputs input[type=file], .formFileInput input[type=file] {
  display: none;
}

input[type=checkbox], input[type=radio] {
  cursor: pointer;
}

input[type=checkbox] + label, input[type=radio] + label {
  cursor: pointer;
}

.radio-item {
  display: flex;
  align-items: center;
}
.radio-item label {
  margin-left: 5px; /* Adjust the spacing between the radio button and its label */
}

input[type=password] {
  letter-spacing: 0.3em;
}

textarea {
  max-width: 100%;
  min-height: 120px;
  line-height: 1.5em;
}

[contenteditable=true]:hover {
  cursor: pointer;
}

.error_msg {
  background: none repeat scroll 0 0 #996666;
  border: 1px solid #FF0000;
  color: #fff;
}

form .requiredwrap {
  display: block;
}
form .requiredlabel:after {
  content: "*";
  color: #FF0000;
}
form label.requiredlabel.error {
  color: #FF0000;
  right: 1px;
  top: 0px;
}
form label.error:after {
  content: " --Required";
  font-weight: bold;
}
form label.requiredlabel.invalid:after,
form label.requiredlabel.error.invalid:after {
  content: " --Invalid";
  font-weight: bold;
}
form input.error,
form input.error:focus,
form input[type=text].error,
form input[type=text].error:focus,
form select.error,
form textarea.error,
form textarea.error:focus {
  background: none repeat scroll 0 0 #996666;
  border: 1px solid #FF0000;
  color: #fff;
}
form input[type=text].error,
form input[type=password].error,
form input[type=datetime],
form input[type=datetime-local].error,
form input[type=date].error,
form input[type=month].error,
form input[type=time].error,
form input[type=week].error,
form input[type=number].error,
form input[type=email].error,
form input[type=url].error,
form input[type=search].error,
form input[type=tel].error,
form input[type=color].error,
form button.error,
form select.error,
form textarea.error {
  margin-bottom: 0 !important;
}
form label.error {
  margin-bottom: 20px;
  display: block;
}

table.xdebug-error, table.xe-warning {
  font-size: 14px !important;
  font-weight: 400 !important;
  font-family: "Courier New", Courier, monospace;
  color: #000 !important;
  color: #000;
  text-decoration: underline;
}
table.xdebug-error:hover, table.xe-warning:hover {
  color: #000;
  text-decoration: underline;
  color: #000 !important;
}
table.xdebug-error:hover:link, table.xdebug-error:hover:visited, table.xe-warning:hover:link, table.xe-warning:hover:visited {
  color: #000;
  text-decoration: underline;
}
table.xdebug-error:hover:hover, table.xe-warning:hover:hover {
  color: #000 !important;
  text-decoration: underline;
}
table.xdebug-error:hover:focus, table.xdebug-error:hover:active, table.xe-warning:hover:focus, table.xe-warning:hover:active {
  color: #000;
}
table.xdebug-error:link, table.xdebug-error:visited, table.xe-warning:link, table.xe-warning:visited {
  color: #000;
  text-decoration: underline;
}
table.xdebug-error:hover, table.xe-warning:hover {
  color: #000 !important;
  text-decoration: underline;
}
table.xdebug-error:focus, table.xdebug-error:active, table.xe-warning:focus, table.xe-warning:active {
  color: #000;
}
table.xdebug-error th td, table.xe-warning th td {
  font-weight: 400 !important;
  color: #000;
  text-decoration: underline;
  color: #000 !important;
}
table.xdebug-error th td:link, table.xdebug-error th td:visited, table.xe-warning th td:link, table.xe-warning th td:visited {
  color: #000;
  text-decoration: underline;
}
table.xdebug-error th td:hover, table.xe-warning th td:hover {
  color: #000 !important;
  text-decoration: underline;
}
table.xdebug-error th td:focus, table.xdebug-error th td:active, table.xe-warning th td:focus, table.xe-warning th td:active {
  color: #000;
}
table.xdebug-error th td:hover, table.xe-warning th td:hover {
  color: #000;
  text-decoration: underline;
  color: #000 !important;
}
table.xdebug-error th td:hover:link, table.xdebug-error th td:hover:visited, table.xe-warning th td:hover:link, table.xe-warning th td:hover:visited {
  color: #000;
  text-decoration: underline;
}
table.xdebug-error th td:hover:hover, table.xe-warning th td:hover:hover {
  color: #000 !important;
  text-decoration: underline;
}
table.xdebug-error th td:hover:focus, table.xdebug-error th td:hover:active, table.xe-warning th td:hover:focus, table.xe-warning th td:hover:active {
  color: #000;
}

#atmedia_test {
  display: none !important;
}

body.is_dev .is_dev {
  display: inherit !important;
}

table {
  width: 100%;
  margin-bottom: 1.5vw;
}
table tr {
  border-bottom: 1px solid #eee;
}
table tr th {
  background-color: #eee;
}
table tr td {
  padding: 7px;
}
table caption {
  margin: 0 0 7px;
  font-size: 0.75em;
  color: #403f3f;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx   */
.tinytable td, .tinytable th {
  padding: 0px 10px 0px 0 !important;
  font-size: 10px;
  text-align: right;
}

.tinytable td:last-child, .tinytable th:last-child {
  padding-right: 0 !important;
}

.tinytable > tbody > tr:last-child td {
  font-weight: bold;
  border-top: 1px solid #999;
}

table.zebra > tbody,
#content table.zebra > tbody,
.content table.zebra > tbody {
  width: 100%;
}
table.zebra > tbody > tr:nth-child(odd),
#content table.zebra > tbody > tr:nth-child(odd),
.content table.zebra > tbody > tr:nth-child(odd) {
  background-color: rgba(118, 45, 36, 0.1);
}
table.zebra > tbody > tr:nth-child(even),
#content table.zebra > tbody > tr:nth-child(even),
.content table.zebra > tbody > tr:nth-child(even) {
  background-color: #fff;
}
table.zebra > tbody > tr > td, table.zebra > tbody > tr > th,
#content table.zebra > tbody > tr > td,
#content table.zebra > tbody > tr > th,
.content table.zebra > tbody > tr > td,
.content table.zebra > tbody > tr > th {
  padding: 2px 10px;
  text-align: left;
}
table.zebra > tbody > tr > th,
#content table.zebra > tbody > tr > th,
.content table.zebra > tbody > tr > th {
  background-color: #762D24;
  font-size: 14px;
  text-transform: uppercase;
  text-align: center !important;
  font-weight: bold;
  color: #fff;
}

table.zebra.borders > tbody > tr:first-child,
#content table.zebra.borders > tbody > tr:first-child,
.content table.zebra.borders > tbody > tr:first-child {
  border-bottom: #403f3f solid 1px;
}
table.zebra.borders > tbody > trlast-child,
#content table.zebra.borders > tbody > trlast-child,
.content table.zebra.borders > tbody > trlast-child {
  border-bottom: #aaa solid 1px;
}
table.zebra.borders > tbody > tr > th,
#content table.zebra.borders > tbody > tr > th,
.content table.zebra.borders > tbody > tr > th {
  border-right: transparent;
}
table.zebra.borders > tbody > tr > th:last-child,
#content table.zebra.borders > tbody > tr > th:last-child,
.content table.zebra.borders > tbody > tr > th:last-child {
  border-right: none;
}
table.zebra.borders > tbody > tr > td,
#content table.zebra.borders > tbody > tr > td,
.content table.zebra.borders > tbody > tr > td {
  border-right: 1px dashed #ddd;
}
table.zebra.borders > tbody > tr > td:last-child,
#content table.zebra.borders > tbody > tr > td:last-child,
.content table.zebra.borders > tbody > tr > td:last-child {
  border-right: none;
}

table.zebra.debug > tbody td,
#content table.zebra.debug > tbody td,
.content table.zebra.debug > tbody td {
  color: #000;
}

.cardscontainer {
  margin: 0 auto;
  width: 100%;
}
.cardscontainer .cardswrapper {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0;
}
@media screen and (max-width: 1150px) {
  .cardscontainer .cardswrapper {
    padding: 0 4vw;
  }
}
.cardscontainer .cardswrapper .cardsgrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 2vw;
}
@media screen and (max-width: 768px) {
  .cardscontainer .cardswrapper .cardsgrid {
    grid-gap: 3.0007501875vw;
  }
}
@media only screen and (max-width: 550px) {
  .cardscontainer .cardswrapper .cardsgrid {
    grid-gap: 4vw;
  }
}
@media screen and (max-width: 1150px) {
  .cardscontainer .cardswrapper .cardsgrid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 768px) {
  .cardscontainer .cardswrapper .cardsgrid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 550px) {
  .cardscontainer .cardswrapper .cardsgrid {
    grid-template-columns: repeat(1, 1fr);
  }
}

.card {
  box-shadow: 2px 3px 3px rgba(0, 0, 0, 0.3), -1px -1px 3px rgba(0, 0, 0, 0.3);
  border-radius: clamp(5px, 1vw, 10px);
  overflow: hidden;
  container-type: inline-size;
  container-name: card;
}
.card a {
  color: #273C3A;
  text-decoration: none;
}
.card a:link, .card a:visited {
  color: #273C3A;
  text-decoration: none;
}
.card a:hover {
  color: #273C3A;
  text-decoration: none;
}
.card a:focus, .card a:active {
  color: #273C3A;
}
.card a h1, .card a .h1, .card a .H1,
.card a h2, .card a .h2, .card a .H2,
.card a h3, .card a .h3, .card a .H3,
.card a h4, .card a .h4, .card a .H4,
.card a h5, .card a .h5, .card a .H5,
.card a h6, .card a .h6, .card a .H6, .card a .headerfont {
  color: #273C3A;
}
.card a h1 a, .card a .h1 a, .card a .H1 a,
.card a h2 a, .card a .h2 a, .card a .H2 a,
.card a h3 a, .card a .h3 a, .card a .H3 a,
.card a h4 a, .card a .h4 a, .card a .H4 a,
.card a h5 a, .card a .h5 a, .card a .H5 a,
.card a h6 a, .card a .h6 a, .card a .H6 a, .card a .headerfont a {
  color: #273C3A;
  text-decoration: none;
}
.card a h1 a:link, .card a h1 a:visited, .card a .h1 a:link, .card a .h1 a:visited, .card a .H1 a:link, .card a .H1 a:visited,
.card a h2 a:link,
.card a h2 a:visited, .card a .h2 a:link, .card a .h2 a:visited, .card a .H2 a:link, .card a .H2 a:visited,
.card a h3 a:link,
.card a h3 a:visited, .card a .h3 a:link, .card a .h3 a:visited, .card a .H3 a:link, .card a .H3 a:visited,
.card a h4 a:link,
.card a h4 a:visited, .card a .h4 a:link, .card a .h4 a:visited, .card a .H4 a:link, .card a .H4 a:visited,
.card a h5 a:link,
.card a h5 a:visited, .card a .h5 a:link, .card a .h5 a:visited, .card a .H5 a:link, .card a .H5 a:visited,
.card a h6 a:link,
.card a h6 a:visited, .card a .h6 a:link, .card a .h6 a:visited, .card a .H6 a:link, .card a .H6 a:visited, .card a .headerfont a:link, .card a .headerfont a:visited {
  color: #273C3A;
  text-decoration: none;
}
.card a h1 a:hover, .card a .h1 a:hover, .card a .H1 a:hover,
.card a h2 a:hover, .card a .h2 a:hover, .card a .H2 a:hover,
.card a h3 a:hover, .card a .h3 a:hover, .card a .H3 a:hover,
.card a h4 a:hover, .card a .h4 a:hover, .card a .H4 a:hover,
.card a h5 a:hover, .card a .h5 a:hover, .card a .H5 a:hover,
.card a h6 a:hover, .card a .h6 a:hover, .card a .H6 a:hover, .card a .headerfont a:hover {
  color: #187177;
  text-decoration: none;
}
.card a h1 a:focus, .card a h1 a:active, .card a .h1 a:focus, .card a .h1 a:active, .card a .H1 a:focus, .card a .H1 a:active,
.card a h2 a:focus,
.card a h2 a:active, .card a .h2 a:focus, .card a .h2 a:active, .card a .H2 a:focus, .card a .H2 a:active,
.card a h3 a:focus,
.card a h3 a:active, .card a .h3 a:focus, .card a .h3 a:active, .card a .H3 a:focus, .card a .H3 a:active,
.card a h4 a:focus,
.card a h4 a:active, .card a .h4 a:focus, .card a .h4 a:active, .card a .H4 a:focus, .card a .H4 a:active,
.card a h5 a:focus,
.card a h5 a:active, .card a .h5 a:focus, .card a .h5 a:active, .card a .H5 a:focus, .card a .H5 a:active,
.card a h6 a:focus,
.card a h6 a:active, .card a .h6 a:focus, .card a .h6 a:active, .card a .H6 a:focus, .card a .H6 a:active, .card a .headerfont a:focus, .card a .headerfont a:active {
  color: #273C3A;
  text-decoration-color: #fff;
}
.card a a h1, .card a a .h1, .card a a .H1,
.card a a h2, .card a a .h2, .card a a .H2,
.card a a h3, .card a a .h3, .card a a .H3,
.card a a h4, .card a a .h4, .card a a .H4,
.card a a h5, .card a a .h5, .card a a .H5,
.card a a h6, .card a a .h6, .card a a .H6,
.card a a .headerfont {
  color: #273C3A;
  text-decoration: none;
}
.card a a h1:link, .card a a h1:visited, .card a a .h1:link, .card a a .h1:visited, .card a a .H1:link, .card a a .H1:visited,
.card a a h2:link,
.card a a h2:visited, .card a a .h2:link, .card a a .h2:visited, .card a a .H2:link, .card a a .H2:visited,
.card a a h3:link,
.card a a h3:visited, .card a a .h3:link, .card a a .h3:visited, .card a a .H3:link, .card a a .H3:visited,
.card a a h4:link,
.card a a h4:visited, .card a a .h4:link, .card a a .h4:visited, .card a a .H4:link, .card a a .H4:visited,
.card a a h5:link,
.card a a h5:visited, .card a a .h5:link, .card a a .h5:visited, .card a a .H5:link, .card a a .H5:visited,
.card a a h6:link,
.card a a h6:visited, .card a a .h6:link, .card a a .h6:visited, .card a a .H6:link, .card a a .H6:visited,
.card a a .headerfont:link,
.card a a .headerfont:visited {
  color: #273C3A;
  text-decoration: none;
}
.card a a h1:hover, .card a a .h1:hover, .card a a .H1:hover,
.card a a h2:hover, .card a a .h2:hover, .card a a .H2:hover,
.card a a h3:hover, .card a a .h3:hover, .card a a .H3:hover,
.card a a h4:hover, .card a a .h4:hover, .card a a .H4:hover,
.card a a h5:hover, .card a a .h5:hover, .card a a .H5:hover,
.card a a h6:hover, .card a a .h6:hover, .card a a .H6:hover,
.card a a .headerfont:hover {
  color: #187177;
  text-decoration: none;
}
.card a a h1:focus, .card a a h1:active, .card a a .h1:focus, .card a a .h1:active, .card a a .H1:focus, .card a a .H1:active,
.card a a h2:focus,
.card a a h2:active, .card a a .h2:focus, .card a a .h2:active, .card a a .H2:focus, .card a a .H2:active,
.card a a h3:focus,
.card a a h3:active, .card a a .h3:focus, .card a a .h3:active, .card a a .H3:focus, .card a a .H3:active,
.card a a h4:focus,
.card a a h4:active, .card a a .h4:focus, .card a a .h4:active, .card a a .H4:focus, .card a a .H4:active,
.card a a h5:focus,
.card a a h5:active, .card a a .h5:focus, .card a a .h5:active, .card a a .H5:focus, .card a a .H5:active,
.card a a h6:focus,
.card a a h6:active, .card a a .h6:focus, .card a a .h6:active, .card a a .H6:focus, .card a a .H6:active,
.card a a .headerfont:focus,
.card a a .headerfont:active {
  color: #273C3A;
  text-decoration-color: #fff;
}
.card .wrap .imgwrap {
  width: 100%;
  margin: 0 auto 1vw;
}
.card .wrap .imgwrap img {
  width: 100%;
}
.card .wrap .textwrap {
  width: 100%;
}
.card .wrap .textwrap .header, .card .wrap .textwrap .blurb, .card .wrap .textwrap .footer {
  margin-bottom: 2vw;
}
.card .wrap .textwrap .headerwrap, .card .wrap .textwrap .blurbwrap, .card .wrap .textwrap .footerwrap {
  padding: 0 1.5vw;
}
@media screen and (max-width: 768px) {
  .card .wrap .textwrap .headerwrap, .card .wrap .textwrap .blurbwrap, .card .wrap .textwrap .footerwrap {
    padding: 0 2vw;
  }
}
.card .wrap .textwrap .header .headerwrap h2 {
  text-align: center;
  margin: 0 auto 1vw;
  hyphens: auto;
  font-size: clamp(1.225em, 8cqi, 1.75em);
  /* Min: 1 rem, Preferred: 4% of container, Max:1.75rem */
}
.card .wrap .textwrap .header .headerwrap .subtitle {
  text-transform: capitalize;
  text-align: center;
  font-style: italic;
  margin: 0 auto;
  hyphens: auto;
  font-size: 14px;
}
@supports (container-type: inline-size) {
  @container (min-width: 290px) {
    .card .wrap .textwrap .header .headerwrap .subtitle {
      font-size: 14.8px;
    }
  }
  @container (min-width: 355px) {
    .card .wrap .textwrap .header .headerwrap .subtitle {
      font-size: 15.6px;
    }
  }
  @container (min-width: 420px) {
    .card .wrap .textwrap .header .headerwrap .subtitle {
      font-size: 16.4px;
    }
  }
  @container (min-width: 485px) {
    .card .wrap .textwrap .header .headerwrap .subtitle {
      font-size: 17.2px;
    }
  }
  @container (min-width: 550px) {
    .card .wrap .textwrap .header .headerwrap .subtitle {
      font-size: 18px;
    }
  }
}
.card .wrap .textwrap .blurb .blurbwrap {
  text-align: left;
}
.card .wrap .textwrap .footer .footerwrap {
  text-align: center;
  overflow-wrap: break-word;
}

.headerblurbcard .wrap {
  width: 100%;
  padding: 2vw;
}
.headerblurbcard .wrap .header .headerwrap h2 {
  text-align: center;
}
.headerblurbcard .wrap .blurb .blurbwrap {
  text-align: left;
}
.headerblurbcard .wrap .footer .footerwrap {
  text-align: center;
  margin: 2vw auto;
}

.wp-caption p.wp-caption-text {
  color: #151d38;
}

.entry-meta {
  font-size: 9px;
  margin: -1% 0 3% 0;
}

hr.separator-break {
  display: block;
  margin: 40px auto !important;
  text-align: center !important;
}

div#comments {
  display: none !important; /* hide for now  */
}

div#spinnerholder {
  background: rgba(0, 0, 0, 0.75);
  display: none;
  height: 100vh;
  text-align: center;
  width: 100vw;
  z-index: 1111;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
div#spinnerholder svg.spinner-svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

svg.spinner-svg {
  width: 200px;
  height: 200px;
}
svg.spinner-svg .gradient-stop {
  stop-color: #fff;
  stop-fill: #fff;
  fill: #fff !important; /* Make sure to set your color here */
}

body #body_wrapper {
  border-top: 1px solid #ccc;
}

#mobile_menu * {
  text-rendering: optimizeLegibility;
  line-height: 1 !important;
}

#mobile_menu {
  background: #fff;
}
#mobile_menu #wprmenu_bar.d_mod {
  background: #fff;
  align-items: center;
  justify-content: space-between;
  margin: 0 !important;
  padding: 3px 0 !important;
  max-width: 100% !important;
  height: auto;
  color: #273C3A;
  font-size: 17px;
}
#mobile_menu #wprmenu_bar.d_mod a {
  color: #273C3A;
  text-decoration: none;
}
#mobile_menu #wprmenu_bar.d_mod a:link, #mobile_menu #wprmenu_bar.d_mod a:visited {
  color: #273C3A;
  text-decoration: none;
}
#mobile_menu #wprmenu_bar.d_mod a:hover {
  color: #273C3A !important;
  text-decoration: underline;
}
#mobile_menu #wprmenu_bar.d_mod a:focus, #mobile_menu #wprmenu_bar.d_mod a:active {
  color: #273C3A;
}
#mobile_menu #wprmenu_bar.d_mod #d_wprm_bar_logo {
  border: none;
  margin: 0 2vw 0 0;
  padding: 0;
}
#mobile_menu #wprmenu_bar.d_mod #d_wprm_bar_logo img.bar_logo {
  max-width: 100%; /* Image can't be wider than its container this makes it shrink to fit */
  max-height: 50px;
  height: auto; /* Maintain aspect ratio */
  width: auto; /* Let it scale naturally */
}
#mobile_menu #wprmenu_bar.d_mod #d_wprm_menu_title {
  width: auto;
  padding: 10px 11px 11px 0;
  text-align: left;
  display: block;
  font-family: "Special Elite", "Times New Roman", Times, serif;
  text-transform: uppercase;
  font-size: 26px;
  color: #273C3A;
}
@media screen and (max-width: 400px) {
  #mobile_menu #wprmenu_bar.d_mod #d_wprm_menu_title {
    font-size: 18px;
  }
}
@media screen and (max-width: 300px) {
  #mobile_menu #wprmenu_bar.d_mod #d_wprm_menu_title {
    font-size: 14px;
  }
}
#mobile_menu #wprmenu_bar.d_mod #d_wprm_bar_logo {
  flex-shrink: 1; /* Allow this to shrink */
  flex-grow: 0; /* Don't grow */
  min-width: 0; /* Allow it to shrink below content size if needed */
}
#mobile_menu #wprmenu_bar.d_mod #d_wprm_menu_title {
  flex-shrink: 1; /* Allow this to shrink too */
  flex-grow: 1; /* Let it take up available space */
  min-width: 0; /* Allow it to shrink below content size if needed */
}
#mobile_menu #wprmenu_bar.d_mod #d_wprm_hamburger {
  flex-shrink: 0; /* Never shrink */
  flex-grow: 0; /* Don't grow */
  top: 1px;
  position: relative;
}

.d_wprm-wrapper *, .wprm-wrapper * {
  text-rendering: optimizeLegibility;
}

.d_wprm-wrapper #mg-wprm-wrap, .wprm-wrapper #mg-wprm-wrap {
  width: 100% !important;
  max-width: none !important;
  background: #fff;
  top: auto !important;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul {
  background-color: #fff;
  padding: 0;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul .wprmenu_icon, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul .wprmenu_icon {
  text-align: right;
  color: #273C3A;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul .wprmenu_icon::before, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul .wprmenu_icon::before {
  color: #273C3A;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item {
  color: #273C3A;
  border-bottom: 1px solid rgba(68, 68, 68, 0.4);
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item:first-child, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item:first-child {
  border-top: 1px solid rgba(68, 68, 68, 0.4);
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a {
  color: #273C3A !important;
  text-decoration: none;
  background-color: transparent;
  font-family: "Special Elite", "Times New Roman", Times, serif;
  font-weight: bold;
  font-size: 20px;
  text-transform: capitalize;
  padding: 2.5% 0 2.5% 2%;
  display: block;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:link, .d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:visited, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:link, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:visited {
  color: #273C3A !important;
  text-decoration: none;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:hover, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:hover {
  color: #273C3A !important;
  text-decoration: none;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:focus, .d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:active, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:focus, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:active {
  color: #273C3A !important;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:hover, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item a:hover {
  background-color: transparent;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item.current-menu-item, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item.current-menu-item {
  background-color: #F2F2F2;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item.current-menu-item > a, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item.current-menu-item > a {
  background-color: transparent;
}
.d_wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item ul li:last-child, .wprm-wrapper #mg-wprm-wrap #wprmenu_menu_ul li.menu-item ul li:last-child {
  border-bottom: 0;
}

#wprmenu_bar.d_mod {
  display: flex !important;
  position: static !important;
}

@media screen and (max-width: 768px) {
  #mainheader_wrapper {
    display: none;
  }
  #mobile_main_header_wrapper {
    display: block;
  }
}
@media screen and (min-width: 769px) {
  #mainheader_wrapper {
    display: block;
  }
  #mobile_main_header_wrapper {
    display: none;
  }
}
@media only screen and (max-width: 768px) {
  html body div.wprm-wrapper {
    overflow: scroll;
  }
  html body div#wpadminbar {
    position: fixed;
  }
  #wprmenu_bar {
    background-size: cover;
    background-repeat: repeat;
    background-color: #c92c2c;
    height: 42px;
    display: block;
  }
  #wprmenu_bar .menu_title {
    color: #ffffff;
  }
  #wprmenu_bar .menu_title a {
    color: #ffffff;
    font-size: 20px;
    font-weight: normal;
  }
  #wprmenu_bar .wprmenu_icon_menu {
    color: #ffffff;
  }
  html body div#mg-wprm-wrap .wpr_submit .icon.icon-search {
    color: #ffffff;
  }
  #mg-wprm-wrap {
    background-color: #c82d2d;
    display: block;
  }
  #mg-wprm-wrap.cbp-spmenu-right, #mg-wprm-wrap.cbp-spmenu-left, #mg-wprm-wrap.cbp-spmenu-right.custom, #mg-wprm-wrap.cbp-spmenu-left.custom, #mg-wprm-wrap.cbp-spmenu-vertical {
    width: 80%;
    max-width: 400px;
  }
  #mg-wprm-wrap li.menu-item a {
    font-size: 15px;
    text-transform: uppercase;
    font-weight: normal;
    color: #ffffff;
  }
  #mg-wprm-wrap li.menu-item a:hover {
    background: #d53f3f;
    color: #ffffff !important;
  }
  #mg-wprm-wrap li.menu-item:valid ~ a {
    color: #ffffff;
  }
  #mg-wprm-wrap li.menu-item-has-children ul.sub-menu a {
    font-size: 15px;
    text-transform: uppercase;
    font-weight: normal;
  }
  #mg-wprm-wrap li.current-menu-item > a {
    background: #d53f3f;
    color: #ffffff !important;
  }
  #mg-wprm-wrap li.current-menu-item span.wprmenu_icon {
    color: #ffffff !important;
  }
  #mg-wprm-wrap ul#wprmenu_menu_ul li.menu-item a {
    color: #ffffff;
  }
  #mg-wprm-wrap ul > li:hover > span.wprmenu_icon {
    color: #ffffff !important;
  }
  div#mg-wprm-wrap ul li span.wprmenu_icon {
    color: #ffffff;
  }
  .cbp-spmenu-push-toright {
    left: 80%;
  }
  .cbp-spmenu-push-toright .mm-slideout {
    left: 80%;
  }
  .cbp-spmenu-push-toleft {
    left: -80%;
  }
  #wprmenu_menu.left {
    width: 80%;
    left: -80%;
    right: auto;
  }
  #wprmenu_menu.right {
    width: 80%;
    right: -80%;
    left: auto;
  }
}
html.wprmenu-body-fixed {
  overflow: inherit;
}
html.wprmenu-body-fixed body {
  position: fixed !important;
}
html body {
  position: relative !important;
}
html body .wprm-overlay.active {
  height: 100%;
  width: 100%;
  z-index: 9999;
  position: fixed;
}
html body div.wprm-wrapper {
  z-index: 999999;
}

.hamburger--slider {
  background-color: transparent;
  overflow: visible;
  text-align: center;
  text-transform: none;
  font: inherit;
  color: inherit;
  padding: 0 !important;
  border: 0;
  margin: 0;
  float: left;
  display: inline-block;
  cursor: pointer;
  transition-property: opacity, filter;
  transition-duration: 0.15s;
  transition-timing-function: linear;
}
.hamburger--slider .hamburger-box {
  width: 100%;
  height: 24px;
  display: inline-block;
  position: relative;
}
.hamburger--slider #menuburgertext {
  font-size: 13px;
  text-transform: lowercase;
  text-align: center;
  color: #273C3A;
  font-family: "Special Elite", "Times New Roman", Times, serif;
}
.hamburger--slider .hamburger-inner,
.hamburger--slider .hamburger-inner::before,
.hamburger--slider .hamburger-inner::after {
  background: #273C3A;
  height: 5px;
  width: 100%;
  display: block;
  border-radius: 4px;
  position: absolute;
  transition-property: transform;
  transition-duration: 0.15s;
  transition-timing-function: ease;
}
.hamburger--slider .hamburger-inner::before,
.hamburger--slider .hamburger-inner::after {
  content: "";
  display: block;
}
.hamburger--slider .hamburger-inner {
  top: 2px;
}
.hamburger--slider .hamburger-inner::before {
  top: 10px;
  transition-property: transform, opacity;
  transition-timing-function: ease;
  transition-duration: 0.15s;
}
.hamburger--slider .hamburger-inner::after {
  top: 20px;
}
.hamburger--slider.is-active .hamburger-inner {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}
.hamburger--slider.is-active .hamburger-inner::before {
  transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
  opacity: 0;
}
.hamburger--slider.is-active .hamburger-inner::after {
  transform: translate3d(0, -20px, 0) rotate(-90deg);
}

nav#main-menu ul#menu-main a {
  font-family: "Special Elite", "Times New Roman", Times, serif;
  color: #F2F2F2 !important;
  color: #F2F2F2 !important;
  text-decoration: none;
  text-transform: none;
  text-decoration: none;
  font-size: 17px;
  padding: 10px;
  display: block;
  position: relative;
}
nav#main-menu ul#menu-main a:link, nav#main-menu ul#menu-main a:visited {
  color: #F2F2F2 !important;
  text-decoration: none;
}
nav#main-menu ul#menu-main a:hover {
  color: #fff !important;
  text-decoration: none;
}
nav#main-menu ul#menu-main a:focus, nav#main-menu ul#menu-main a:active {
  color: #F2F2F2 !important;
}
nav#main-menu ul#menu-main a:focus, nav#main-menu ul#menu-main a:visited {
  text-decoration: none;
  text-decoration-color: #762D24 !important;
}
nav#main-menu ul#menu-main a:hover {
  text-decoration-color: #fff;
  text-decoration: underline !important;
}
nav#main-menu ul#menu-main > li {
  float: left;
}
nav#main-menu ul#menu-main > li:last-child a {
  padding-right: 0;
}
nav#main-menu ul#menu-main > li.menu-item-has-children ul.sub-menu {
  display: none;
}
nav#main-menu ul#menu-main > li.menu-item-has-children:hover ul.sub-menu {
  border-bottom: 0 !important;
  background: #762D24;
  display: block;
  margin-top: 0;
  position: absolute;
  z-index: 8999;
}

nav#main_menu_flex .menu-main-container {
  width: 100%;
}
nav#main_menu_flex .menu-main-container ul#menu-main {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item {
  position: relative;
  margin: 0;
  padding: 0;
  z-index: 9;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item:last-child {
  z-index: 1;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item:nth-last-child(2) {
  z-index: 2;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item:nth-last-child(3) {
  z-index: 3;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item:nth-last-child(4) {
  z-index: 4;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item a {
  font-family: "Special Elite", "Times New Roman", Times, serif;
  color: #F2F2F2;
  text-decoration: none;
  display: block;
  padding: 10px 15px;
  font-size: 17px;
  white-space: nowrap;
  text-transform: uppercase;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item a:link, nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item a:visited {
  color: #F2F2F2;
  text-decoration: none;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item a:hover {
  color: #fff;
  text-decoration: underline;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item a:focus, nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item a:active {
  color: #F2F2F2;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.current-menu-item > a {
  text-decoration: underline;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children > a {
  position: relative;
  padding-right: 20px;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children > a::after {
  content: " ▾";
  font-size: 0.8em;
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  color: inherit;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children ul.sub-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 999;
  background: #762D24;
  min-width: 200px;
  padding: 5px 0;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children ul.sub-menu li {
  display: block;
  width: 100%;
  position: relative;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children ul.sub-menu li a {
  padding: 8px 15px;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children ul.sub-menu li.menu-item-has-children > a::after {
  content: " ▸";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children ul.sub-menu li.menu-item-has-children > ul.sub-menu {
  display: none;
  position: absolute;
  top: 0;
  left: 100%;
  min-width: 200px;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children ul.sub-menu li.menu-item-has-children:hover > ul.sub-menu {
  display: block;
}
nav#main_menu_flex .menu-main-container ul#menu-main li.menu-item.menu-item-has-children:hover > ul.sub-menu {
  display: block;
}
nav#main_menu_flex .menu-main-container ul#menu-main > li:not(:last-child)::after {
  content: "•";
  display: inline-block;
  color: #F2F2F2;
  margin: 0 5px;
  position: absolute;
  right: -8px;
  top: 42%;
  transform: translateY(-50%);
}

h1, .h1, .H1,
h2, .h2, .H2,
h3, .h3, .H3,
h4, .h4, .H4,
h5, .h5, .H5,
h6, .h6, .H6, .headerfont {
  text-rendering: optimizelegibility;
  font-family: "Special Elite", "Times New Roman", Times, serif;
  font-weight: 900;
  text-transform: capitalize;
  color: #273C3A;
  clear: both;
}
h1 a, .h1 a, .H1 a,
h2 a, .h2 a, .H2 a,
h3 a, .h3 a, .H3 a,
h4 a, .h4 a, .H4 a,
h5 a, .h5 a, .H5 a,
h6 a, .h6 a, .H6 a, .headerfont a {
  text-decoration: none; /* removing text decoration from all headline links */
  color: #273C3A;
  text-decoration: none !important;
}
h1 a:link, h1 a:visited, .h1 a:link, .h1 a:visited, .H1 a:link, .H1 a:visited,
h2 a:link,
h2 a:visited, .h2 a:link, .h2 a:visited, .H2 a:link, .H2 a:visited,
h3 a:link,
h3 a:visited, .h3 a:link, .h3 a:visited, .H3 a:link, .H3 a:visited,
h4 a:link,
h4 a:visited, .h4 a:link, .h4 a:visited, .H4 a:link, .H4 a:visited,
h5 a:link,
h5 a:visited, .h5 a:link, .h5 a:visited, .H5 a:link, .H5 a:visited,
h6 a:link,
h6 a:visited, .h6 a:link, .h6 a:visited, .H6 a:link, .H6 a:visited, .headerfont a:link, .headerfont a:visited {
  color: #273C3A;
  text-decoration: none !important;
}
h1 a:hover, .h1 a:hover, .H1 a:hover,
h2 a:hover, .h2 a:hover, .H2 a:hover,
h3 a:hover, .h3 a:hover, .H3 a:hover,
h4 a:hover, .h4 a:hover, .H4 a:hover,
h5 a:hover, .h5 a:hover, .H5 a:hover,
h6 a:hover, .h6 a:hover, .H6 a:hover, .headerfont a:hover {
  color: #187177 !important;
  text-decoration: none !important;
}
h1 a:focus, h1 a:active, .h1 a:focus, .h1 a:active, .H1 a:focus, .H1 a:active,
h2 a:focus,
h2 a:active, .h2 a:focus, .h2 a:active, .H2 a:focus, .H2 a:active,
h3 a:focus,
h3 a:active, .h3 a:focus, .h3 a:active, .H3 a:focus, .H3 a:active,
h4 a:focus,
h4 a:active, .h4 a:focus, .h4 a:active, .H4 a:focus, .H4 a:active,
h5 a:focus,
h5 a:active, .h5 a:focus, .h5 a:active, .H5 a:focus, .H5 a:active,
h6 a:focus,
h6 a:active, .h6 a:focus, .h6 a:active, .H6 a:focus, .H6 a:active, .headerfont a:focus, .headerfont a:active {
  color: #273C3A;
}

.darkbg h1, .darkbg .h1, .darkbg .H1,
.darkbg h2, .darkbg .h2, .darkbg .H2,
.darkbg h3, .darkbg .h3, .darkbg .H3,
.darkbg h4, .darkbg .h4, .darkbg .H4,
.darkbg h5, .darkbg .h5, .darkbg .H5,
.darkbg h6, .darkbg .h6, .darkbg .H6, .darkbg .headerfont {
  color: #ECDCD3;
}
.darkbg h1 a, .darkbg .h1 a, .darkbg .H1 a,
.darkbg h2 a, .darkbg .h2 a, .darkbg .H2 a,
.darkbg h3 a, .darkbg .h3 a, .darkbg .H3 a,
.darkbg h4 a, .darkbg .h4 a, .darkbg .H4 a,
.darkbg h5 a, .darkbg .h5 a, .darkbg .H5 a,
.darkbg h6 a, .darkbg .h6 a, .darkbg .H6 a, .darkbg .headerfont a {
  text-decoration: none; /* <-- removing text decoration from all headline links */
  color: #ECDCD3;
  text-decoration: none;
}
.darkbg h1 a:link, .darkbg h1 a:visited, .darkbg .h1 a:link, .darkbg .h1 a:visited, .darkbg .H1 a:link, .darkbg .H1 a:visited,
.darkbg h2 a:link,
.darkbg h2 a:visited, .darkbg .h2 a:link, .darkbg .h2 a:visited, .darkbg .H2 a:link, .darkbg .H2 a:visited,
.darkbg h3 a:link,
.darkbg h3 a:visited, .darkbg .h3 a:link, .darkbg .h3 a:visited, .darkbg .H3 a:link, .darkbg .H3 a:visited,
.darkbg h4 a:link,
.darkbg h4 a:visited, .darkbg .h4 a:link, .darkbg .h4 a:visited, .darkbg .H4 a:link, .darkbg .H4 a:visited,
.darkbg h5 a:link,
.darkbg h5 a:visited, .darkbg .h5 a:link, .darkbg .h5 a:visited, .darkbg .H5 a:link, .darkbg .H5 a:visited,
.darkbg h6 a:link,
.darkbg h6 a:visited, .darkbg .h6 a:link, .darkbg .h6 a:visited, .darkbg .H6 a:link, .darkbg .H6 a:visited, .darkbg .headerfont a:link, .darkbg .headerfont a:visited {
  color: #ECDCD3;
  text-decoration: none;
}
.darkbg h1 a:hover, .darkbg .h1 a:hover, .darkbg .H1 a:hover,
.darkbg h2 a:hover, .darkbg .h2 a:hover, .darkbg .H2 a:hover,
.darkbg h3 a:hover, .darkbg .h3 a:hover, .darkbg .H3 a:hover,
.darkbg h4 a:hover, .darkbg .h4 a:hover, .darkbg .H4 a:hover,
.darkbg h5 a:hover, .darkbg .h5 a:hover, .darkbg .H5 a:hover,
.darkbg h6 a:hover, .darkbg .h6 a:hover, .darkbg .H6 a:hover, .darkbg .headerfont a:hover {
  color: #ddd !important;
  text-decoration: none;
}
.darkbg h1 a:focus, .darkbg h1 a:active, .darkbg .h1 a:focus, .darkbg .h1 a:active, .darkbg .H1 a:focus, .darkbg .H1 a:active,
.darkbg h2 a:focus,
.darkbg h2 a:active, .darkbg .h2 a:focus, .darkbg .h2 a:active, .darkbg .H2 a:focus, .darkbg .H2 a:active,
.darkbg h3 a:focus,
.darkbg h3 a:active, .darkbg .h3 a:focus, .darkbg .h3 a:active, .darkbg .H3 a:focus, .darkbg .H3 a:active,
.darkbg h4 a:focus,
.darkbg h4 a:active, .darkbg .h4 a:focus, .darkbg .h4 a:active, .darkbg .H4 a:focus, .darkbg .H4 a:active,
.darkbg h5 a:focus,
.darkbg h5 a:active, .darkbg .h5 a:focus, .darkbg .h5 a:active, .darkbg .H5 a:focus, .darkbg .H5 a:active,
.darkbg h6 a:focus,
.darkbg h6 a:active, .darkbg .h6 a:focus, .darkbg .h6 a:active, .darkbg .H6 a:focus, .darkbg .H6 a:active, .darkbg .headerfont a:focus, .darkbg .headerfont a:active {
  color: #ECDCD3;
}

h1, .h1, .H1,
h2, .h2, .H2,
h3, .h3, .H3,
h4, .h4, .H4,
h5, .h5, .H5,
h6, .h6, .H6, .headerfont {
  margin-right: 0;
  margin-left: 0;
  margin-top: -0.18em;
  margin-bottom: 0.6em;
  line-height: 1.1;
}

h1, .h1, .H1 {
  font-size: 2.3em;
}

h2, .h2, .H2 {
  font-size: 1.75em;
}

h3, .h3, .H3 {
  font-size: 1.5em;
}

h4, .h4, .H4 {
  font-size: 1.23em;
}

h5, .h5, .H5 {
  font-size: 1em;
}

h6, .h6, .H6 {
  font-size: 0.9em;
}

.fl-button {
  background: #C34436 !important;
  color: #F2F2F2 !important;
  font-size: 14px;
  line-height: 16px;
  border-radius: 4px;
  border: 1px solid #ccc;
  padding: 10px 20px 11px;
}
.fl-button span,
.fl-button .fl-button-icon {
  color: #fff !important;
}

/* BUTTON DEFAULTS We're gonna use a placeholder selector here so we can use common styles. We then use this
to load up the defaults in all our buttons.   Here's a quick video to show how it works:http://www.youtube.com/watch?v=hwdVpKiJzac  */
.blue-btn {
  display: inline-block;
  position: relative;
  text-decoration: none;
  color: #fff;
  font-size: 0.9em;
  line-height: 34px;
  font-weight: normal;
  padding: 0 24px;
  border-radius: 4px;
  border: 0;
  cursor: pointer;
  transition: background-color 0.24s ease-in-out;
}
.blue-btn:hover, .blue-btn:focus {
  color: #fff;
  text-decoration: none;
  outline: none;
}
.blue-btn:active {
  top: 1px;
}

.blue-btn {
  background-color: #187177;
}
.blue-btn:hover, .blue-btn:focus {
  background-color: rgb(20.5762237762, 96.8797202797, 102.0237762238);
}
.blue-btn:active {
  background-color: rgb(19.7202797203, 92.8496503497, 97.7797202797);
}

#footers_container {
  margin: 0 auto;
  width: 100%;
  background: #762D24;
  margin-top: 4vw;
  color: #F2F2F2;
  margin-bottom: 8vw;
}
#footers_container #mainfooter_container {
  margin: 0 auto;
  width: 100%;
}
#footers_container #mainfooter_container #mainfooter_wrapper {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0;
  padding-top: 2vw !important;
  padding-bottom: 2vw !important;
  display: grid;
  grid-gap: 2vw;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
}
@media screen and (max-width: 1150px) {
  #footers_container #mainfooter_container #mainfooter_wrapper {
    padding: 0 4vw;
  }
}
#footers_container #mainfooter_container #mainfooter_wrapper a {
  color: #F2F2F2;
  text-decoration: none;
}
#footers_container #mainfooter_container #mainfooter_wrapper a:link, #footers_container #mainfooter_container #mainfooter_wrapper a:visited {
  color: #F2F2F2;
  text-decoration: none;
}
#footers_container #mainfooter_container #mainfooter_wrapper a:hover {
  color: #fff;
  text-decoration: none;
}
#footers_container #mainfooter_container #mainfooter_wrapper a:focus, #footers_container #mainfooter_container #mainfooter_wrapper a:active {
  color: #F2F2F2;
}
#footers_container #mainfooter_container #mainfooter_wrapper #left {
  text-align: left;
}
#footers_container #mainfooter_container #mainfooter_wrapper #right {
  text-align: right;
}
#footers_container #mainfooter_container #mainfooter_wrapper #right form {
  display: inline-block;
}
#footers_container #mainfooter_container #mainfooter_wrapper #right form input {
  float: right;
}
#footers_container #mainfooter_container #mainfooter_wrapper #right form label {
  display: none;
}

body.is_front_page #footers_container,
body.fullwidth #footers_container {
  margin-top: -1px !important;
}

#headers_container {
  width: 100%;
  background-color: #762D24;
}
#headers_container #mainheader_container {
  margin: 0 auto;
  width: 100%;
}
#headers_container #mainheader_container #mainheader_wrapper {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0;
}
@media screen and (max-width: 1150px) {
  #headers_container #mainheader_container #mainheader_wrapper {
    padding: 0 4vw;
  }
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text {
  float: left;
  margin: 0;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text a {
  color: #F2F2F2;
  text-decoration: none;
  text-decoration: none !important;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text a:link, #headers_container #mainheader_container #mainheader_wrapper #logo-and-text a:visited {
  color: #F2F2F2;
  text-decoration: none;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text a:hover {
  color: #fff;
  text-decoration: none;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text a:focus, #headers_container #mainheader_container #mainheader_wrapper #logo-and-text a:active {
  color: #F2F2F2;
}
#headers_container #mainheader_container #mainheader_wrapper #menu {
  float: right;
  margin: 16px 0 0 0 !important;
}
#headers_container #mobile_main_header_wrapper {
  background: #fff;
  padding: 0 2vw;
}

body #body_container {
  width: 100%;
  max-width: 2000px;
}
body #body_container #body_wrapper {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 4vw 0 0 0;
}
@media screen and (max-width: 1150px) {
  body #body_container #body_wrapper {
    padding: 4vw 4vw 0;
  }
}
body.fullwidth #body_container #body_wrapper {
  max-width: 100% !important;
  padding: 0 !important;
}
@media screen and (max-width: 1150px) {
  body.fullwidth #body_container #body_wrapper {
    padding: 0 !important;
  }
}
@media screen and (max-width: 768px) {
  body.fullwidth #body_container #body_wrapper {
    padding: 0 !important;
  }
}
body.fullwidth #body_container .entry-content > div:last-child,
body.fullwidth #body_container #post_wrapper > div:last-child,
body.fullwidth #body_container div#content > div:last-child {
  padding-bottom: 0;
}

#body_wrapper .fl-builder-module-template {
  padding: 0;
}
#body_wrapper .fl-builder-content-primary > .fl-row {
  margin-bottom: 4vw;
}
#body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap {
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding: 0 !important;
}
#body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col {
  padding: 0 2vw;
}
#body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col:first-child {
  padding-left: 0;
}
#body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col:last-child {
  padding-right: 0;
}
#body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col p:last-child {
  margin-bottom: 0;
}
#body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col .fl-post-grid {
  margin-left: 0;
  margin-right: 0;
}
@media (max-width: 550px) {
  #body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col-small:not(.fl-col-small-full-width) {
    max-width: 100%;
    visibility: visible;
    padding: 0;
    margin: 16vw 0;
  }
  #body_wrapper .fl-builder-content-primary > .fl-row .fl-row-content-wrap .fl-col-small:not(.fl-col-small-full-width):first-child {
    margin: 0 0 16vw 0;
  }
}
#body_wrapper .fl-row.fl-row-fixed-width {
  padding: 0;
}
#body_wrapper .fl-row.fl-row-fixed-width .fl-row-fixed-width {
  padding: 0;
}

@media screen and (max-width: 1150px) {
  body.fullwidth div.fl-row.fl-row-fixed-width,
  body.fullwidth #body_container #body_wrapper div.fl-row.fl-row-full-width.fullbutpadded div.fl-row-content-wrap {
    padding: 0 4vw !important;
  }
}

.fl-builder-content .fl-module-content,
.fl-builder-template .fl-module-content,
.fl-builder-row-template .fl-module-content {
  margin: 0 !important;
}

div.fl-module-post-grid div.fl-post-column .fl-post-grid-text {
  padding: 1vw !important;
}
div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-title {
  font-size: clamp(18px, 14px + 0.7272727273vw, 22px);
  line-height: 1.2 !important;
}
div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-title, div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-title a {
  text-decoration: none !important;
}
div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-meta, div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-meta p, div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-meta span,
div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-content,
div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-content p,
div.fl-module-post-grid div.fl-post-column .fl-post-grid-text .fl-post-grid-content span {
  font-size: clamp(15px, 13px + 0.3636363636vw, 17px);
  line-height: 1.4 !important;
}

div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column {
  padding: 0 !important;
  width: 18.39% !important;
  margin-bottom: 2%;
  margin-right: 2%;
}
div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column:nth-child(5n) {
  margin-right: 0 !important;
}
@media screen and (max-width: 1200px) {
  div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column {
    width: 22.99% !important;
  }
  div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column:nth-child(4n) {
    margin-right: 0 !important;
  }
}
@media screen and (max-width: 992px) {
  div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column {
    width: 31.32% !important;
  }
  div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column:nth-child(3n) {
    margin-right: 0 !important;
  }
}
@media screen and (max-width: 768px) {
  div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column {
    width: 47.95% !important;
  }
  div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX div.fl-post-column:nth-child(2n) {
    margin-right: 0 !important;
  }
}

div.fl-post-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 2vw;
  gap: 1.5vw;
  padding: 0 !important;
  margin: 0 auto !important;
}
@media screen and (max-width: 768px) {
  div.fl-post-grid {
    grid-gap: 3.0007501875vw;
  }
}
@media only screen and (max-width: 550px) {
  div.fl-post-grid {
    grid-gap: 4vw;
  }
}
@media screen and (max-width: 1150px) {
  div.fl-post-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media screen and (max-width: 768px) {
  div.fl-post-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (max-width: 550px) {
  div.fl-post-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
div.fl-post-grid div.fl-post-column {
  width: auto;
  min-width: 0;
  padding: 0 !important;
  margin: 0 !important;
}
div.fl-post-grid:before, div.fl-post-grid:after, div.fl-post-grid:not([data-accepts]):before, div.fl-post-grid:not([data-accepts]):after {
  display: none !important;
}

.fl-module-heading .fl-heading {
  margin-bottom: 2vw !important;
}

.d_createGallery {
  margin: 0;
  padding: 0;
  width: 100%;
}
.d_createGallery .d_createGallery__item {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
}
.d_createGallery .d_createGallery__item img {
  display: block;
  width: 100%;
}
.d_createGallery .d_createGallery__item img, .d_createGallery .d_createGallery__item p, .d_createGallery .d_createGallery__item div, .d_createGallery .d_createGallery__item a, .d_createGallery .d_createGallery__item span {
  margin: 0 !important;
  padding: 0;
}

#d_createGallery.columns {
  display: grid;
  gap: 24px;
}
#d_createGallery.columns.portraits .d_createGallery__item a {
  aspect-ratio: 4/5; /* Set the desired aspect ratio */
  display: block;
}
#d_createGallery.columns.portraits .d_createGallery__item a img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
#d_createGallery.columns.cols_6 {
  grid-template-columns: repeat(6, 1fr);
}
#d_createGallery.columns.cols_5 {
  grid-template-columns: repeat(5, 1fr);
}
#d_createGallery.columns.cols_4 {
  grid-template-columns: repeat(4, 1fr);
}
#d_createGallery.columns.cols_3 {
  grid-template-columns: repeat(3, 1fr);
}
@media only screen and (max-width: 550px) {
  #d_createGallery.columns {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1vw !important;
  }
}

.d_createGallery.masonry #masonrygutter {
  width: 1.5%;
}
.d_createGallery.masonry .d_createGallery__item {
  margin: 0 0 1.5% 0;
}
.d_createGallery.masonry .d_createGallery__item.cols_2 {
  max-width: calc((100% - 1.5% * (2 - 1)) / 2);
}
.d_createGallery.masonry .d_createGallery__item.cols_3 {
  max-width: calc((100% - 1.5% * (3 - 1)) / 3);
}
.d_createGallery.masonry .d_createGallery__item.cols_4 {
  max-width: calc((100% - 1.5% * (4 - 1)) / 4);
}
.d_createGallery.masonry .d_createGallery__item.cols_5 {
  max-width: calc((100% - 1.5% * (5 - 1)) / 5);
}
.d_createGallery.masonry .d_createGallery__item.cols_6 {
  max-width: calc((100% - 1.5% * (6 - 1)) / 6);
}
.d_createGallery.masonry .d_createGallery__item.cols_7 {
  max-width: calc((100% - 1.5% * (7 - 1)) / 7);
}
@media only screen and (max-width: 550px) {
  .d_createGallery.masonry .d_createGallery__item.cols_4 {
    max-width: calc((100% - 1.5% * 2) / 3);
  }
  .d_createGallery.masonry .d_createGallery__item.cols_5 {
    max-width: calc((100% - 1.5% * 2) / 3);
  }
  .d_createGallery.masonry .d_createGallery__item.cols_6 {
    max-width: calc((100% - 1.5% * 2) / 3);
  }
  .d_createGallery.masonry .d_createGallery__item.cols_7 {
    max-width: calc((100% - 1.5% * 2) / 3);
  }
}

.d_createGallery.hoverup .d_createGallery__item {
  overflow: hidden;
}
.d_createGallery.hoverup .d_createGallery__item .d_createGallery__title {
  text-align: left;
  position: absolute;
  width: 100%;
  padding: 2% 1% 3% 3% !important;
  font-size: 13px;
  background-color: rgba(255, 255, 255, 0.85);
  color: #151d38;
  opacity: 0;
  bottom: -42px;
  transition: all 0.6s ease;
}
@media only screen and (max-width: 550px) {
  .d_createGallery.hoverup .d_createGallery__item .d_createGallery__title {
    display: none;
  }
}
.d_createGallery.hoverup .d_createGallery__item:hover .d_createGallery__title {
  opacity: 1;
  bottom: -1px;
}
.d_createGallery.hoverup .d_createGallery__item .d_createGallery__caption, .d_createGallery.hoverup .d_createGallery__item .d_createGallery__description {
  display: none;
}
.d_createGallery.hoverup .d_createGallery__item.has_caption:before {
  content: "🔍";
  color: black;
  font-size: clamp(11px, 2vw, 13px);
  line-height: normal;
  text-align: center;
  background: rgba(255, 255, 255, 0.55);
  border-radius: 2vw;
  width: 3vw;
  height: 3vw;
  max-width: 20px;
  max-height: 20px;
  transition: all 0.6s ease;
  opacity: 0.75;
  position: absolute;
  bottom: 1px;
  right: 1px;
}
@media only screen and (max-width: 550px) {
  .d_createGallery.hoverup .d_createGallery__item.has_caption:before {
    display: none;
  }
}
.d_createGallery.hoverup .d_createGallery__item.has_caption:hover:before {
  opacity: 0;
}

.d_createGallery.under.columns {
  gap: 4vw 2vw !important;
}
.d_createGallery.under .d_createGallery__item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1vw;
}
.d_createGallery.under .d_createGallery__item .d_createGallery__title {
  margin-top: 0 !important;
  padding: 1.5vw 1vw 0 !important;
  color: #151d38;
  font-weight: 400;
  font-family: "Special Elite", "Times New Roman", Times, serif;
  font-size: 27px;
  z-index: 2;
}
.d_createGallery.under .d_createGallery__item .d_createGallery__title,
.d_createGallery.under .d_createGallery__item .d_createGallery__caption,
.d_createGallery.under .d_createGallery__item .d_createGallery__description {
  line-height: 1;
  width: 100%;
  text-align: center;
}
.d_createGallery.under .d_createGallery__item .d_createGallery__caption.titleandcaps {
  margin-top: 0.5vw;
}

.show_d_gallery_of_galleries__each {
  float: left;
  width: 48%;
  margin-bottom: 4%;
  margin-right: 4%;
}

.show_d_gallery_of_galleries__each .post-img img {
  width: 100%;
}

.show_d_gallery_of_galleries__each:nth-of-type(2n+2) {
  margin-right: 0;
}

@media screen and (max-width: 480px) {
  .show_d_gallery_of_galleries__each {
    float: none;
    width: 100%;
    max-width: 100%;
    margin-bottom: 4% !important;
    margin-right: 0 !important;
  }
}
.gslide .ginner-container.desc-bottom .gslide-image img {
  width: auto;
}
.gslide .gslide-description {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 100%;
  flex: 1 0 100%;
}
.gslide .gslide-description.description-bottom, .gslide .gslide-description.description-top {
  margin: 0 auto;
  width: 100%;
}
.gslide .gslide-description p {
  margin-bottom: 12px;
}
.gslide .gslide-description p:last-child {
  margin-bottom: 0;
}
.gslide .gslide-image {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.gslide .gslide-image img {
  max-height: 100vh;
  display: block;
  padding: 0;
  float: none;
  outline: none;
  border: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  max-width: 100vw;
  width: auto;
  height: auto;
  -o-object-fit: cover;
  object-fit: cover;
  -ms-touch-action: none;
  touch-action: none;
  margin: auto;
  min-width: 200px;
}

.zoomed .gslide-description {
  display: none;
}

body.glightbox-mobile .glightbox-container .gslide-description {
  height: auto !important;
  width: 100%;
  position: absolute;
  bottom: 2px;
  padding: 2vw 0;
  max-width: 100vw !important;
  -webkit-box-ordinal-group: 3 !important;
  -ms-flex-order: 2 !important;
  order: 2 !important;
  max-height: 78vh;
  overflow: auto !important;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
  -webkit-transition: opacity 0.3s linear;
  transition: opacity 0.3s linear;
  padding-bottom: 50px;
}
body.glightbox-mobile .glightbox-container .gslide-title {
  color: #273C3A;
  font-size: 1em;
}
body.glightbox-mobile .glightbox-container .gslide-desc {
  color: rgba(39, 60, 58, 0.8);
}
body.glightbox-mobile .glightbox-container .gslide-desc a {
  color: #273C3A;
  font-weight: bold;
}
body.glightbox-mobile .glightbox-container .gslide-desc .desc-more {
  color: #273C3A;
  opacity: 0.4;
}
body.glightbox-mobile .goverlay {
  background: #000;
}

.gdesc-open .gdesc-inner {
  padding-bottom: 30px;
}

.glightbox-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999 !important;
  overflow: hidden;
  -ms-touch-action: none;
  touch-action: none;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  outline: none;
}
.glightbox-container.inactive {
  display: none;
}
.glightbox-container .gcontainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 9999;
  overflow: hidden;
}
.glightbox-container .gslider {
  -webkit-transition: -webkit-transform 0.4s ease;
  transition: -webkit-transform 0.4s ease;
  transition: transform 0.4s ease;
  transition: transform 0.4s ease, -webkit-transform 0.4s ease;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
  position: relative;
  overflow: hidden;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.glightbox-container .gslide {
  width: 100%;
  position: absolute;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  opacity: 0;
}
.glightbox-container .gslide.current {
  opacity: 1;
  z-index: 99999;
  position: relative;
}
.glightbox-container .gslide.prev {
  opacity: 1;
  z-index: 9999;
}
.glightbox-container .gslide-inner-content {
  width: 100%;
}
.glightbox-container .ginner-container {
  position: relative;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 100%;
  margin: auto;
  height: 100vh;
}
.glightbox-container .ginner-container.gvideo-container {
  width: 100%;
}
.glightbox-container .ginner-container.desc-bottom,
.glightbox-container .ginner-container.desc-top {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.glightbox-container .ginner-container.desc-left,
.glightbox-container .ginner-container.desc-right {
  max-width: 100% !important;
}

.gslide iframe,
.gslide video {
  outline: none !important;
  border: none;
  min-height: 165px;
  -webkit-overflow-scrolling: touch;
  -ms-touch-action: auto;
  touch-action: auto;
}

.gslide:not(.current) {
  pointer-events: none;
}

.desc-top .gslide-image img,
.desc-left .gslide-image img,
.desc-right .gslide-image img {
  width: auto;
  max-width: 100%;
}

.gslide-image img.zoomable {
  position: relative;
}

.gslide-image img.dragging {
  cursor: grabbing !important;
  -webkit-transition: none;
  transition: none;
}

.gslide-video {
  position: relative;
  max-width: 100vh;
  width: 100% !important;
}

.gslide-video .plyr__poster-enabled.plyr--loading .plyr__poster {
  display: none;
}

.gslide-video .gvideo-wrapper {
  width: 100%;
  /* max-width: 160vmin; */
  margin: auto;
}

.gslide-video::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 0, 0.34);
  display: none;
}

.gslide-video.playing::before {
  display: none;
}

.gslide-video.fullscreen {
  max-width: 100% !important;
  min-width: 100%;
  height: 75vh;
}

.gslide-video.fullscreen video {
  max-width: 100% !important;
  width: 100% !important;
}

.gslide-inline {
  background: #fff;
  text-align: left;
  max-height: calc(100vh - 40px);
  overflow: auto;
  max-width: 100%;
  margin: auto;
}

.gslide-inline .ginlined-content {
  padding: 20px;
  width: 100%;
}

.gslide-inline .dragging {
  cursor: grabbing !important;
  -webkit-transition: none;
  transition: none;
}

.ginlined-content {
  overflow: auto;
  display: block !important;
  opacity: 1;
}

.gslide-external {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  min-width: 100%;
  background: #fff;
  padding: 0;
  overflow: auto;
  max-height: 75vh;
  height: 100%;
}

.gslide-media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.zoomed .gslide-media {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.desc-top .gslide-media,
.desc-bottom .gslide-media {
  margin: 0 auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.gslide-description.description-left,
.gslide-description.description-right {
  max-width: 100%;
}

.glightbox-button-hidden {
  display: none;
}

.gdesc-open .gslide-media {
  -webkit-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease;
  opacity: 0.4;
}

.gdesc-closed .gslide-media {
  -webkit-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease;
  opacity: 1;
}

.greset {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.gabsolute {
  position: absolute;
}

.grelative {
  position: relative;
}

.glightbox-desc {
  display: none !important;
}

.glightbox-open {
  overflow: hidden;
}

.gloader {
  height: 25px;
  width: 25px;
  -webkit-animation: lightboxLoader 0.8s infinite linear;
  animation: lightboxLoader 0.8s infinite linear;
  border: 2px solid #fff;
  border-right-color: transparent;
  border-radius: 50%;
  position: absolute;
  display: block;
  z-index: 9999;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 47%;
}

.goverlay {
  width: 100%;
  height: calc(100vh + 1px);
  position: fixed;
  top: -1px;
  left: 0;
  background: #000;
  will-change: opacity;
}

.gprev,
.gnext,
.gclose {
  z-index: 99999;
  cursor: pointer;
  width: 26px;
  height: 44px;
  border: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.gprev svg,
.gnext svg,
.gclose svg {
  display: block;
  width: 25px;
  height: auto;
  margin: 0;
  padding: 0;
}

.gprev.disabled,
.gnext.disabled,
.gclose.disabled {
  opacity: 0.1;
}

.gprev .garrow,
.gnext .garrow,
.gclose .garrow {
  stroke: #fff;
}

.gbtn.focused {
  outline: 2px solid #0f3d81;
}

iframe.wait-autoplay {
  opacity: 0;
}

.glightbox-closing .gnext,
.glightbox-closing .gprev,
.glightbox-closing .gclose {
  opacity: 0 !important;
}

/*Skin */
.glightbox-clean .gslide-description {
  background: #fff;
}
.glightbox-clean .gslide-description .gdesc-inner {
  padding: 2vw 2vw;
  background: #fff;
}
.glightbox-clean .gslide-description .gdesc-inner .gslide-title {
  margin-bottom: 1vw;
  color: #403f3f;
}
.glightbox-clean .gslide-description .gdesc-inner .gslide-desc {
  color: #403f3f;
}
.glightbox-clean .gslide-video {
  background: #fff;
}
.glightbox-clean .gprev,
.glightbox-clean .gnext,
.glightbox-clean .gclose {
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 4px;
}
.glightbox-clean .gprev path,
.glightbox-clean .gnext path,
.glightbox-clean .gclose path {
  fill: #fff;
}
.glightbox-clean .gprev {
  position: absolute;
  top: -100%;
  left: 30px;
  width: 40px;
  height: 50px;
}
.glightbox-clean .gnext {
  position: absolute;
  top: -100%;
  right: 30px;
  width: 40px;
  height: 50px;
}
.glightbox-clean .gclose {
  width: 35px;
  height: 35px;
  top: 15px;
  right: 10px;
  position: absolute;
}
.glightbox-clean .gclose svg {
  width: 18px;
  height: auto;
}
.glightbox-clean .gclose:hover {
  opacity: 1;
}

.gfadeIn {
  -webkit-animation: gfadeIn 0.5s ease;
  animation: gfadeIn 0.5s ease;
}

.gfadeOut {
  -webkit-animation: gfadeOut 0.5s ease;
  animation: gfadeOut 0.5s ease;
}

.gslideOutLeft {
  -webkit-animation: gslideOutLeft 0.3s ease;
  animation: gslideOutLeft 0.3s ease;
}

.gslideInLeft {
  -webkit-animation: gslideInLeft 0.3s ease;
  animation: gslideInLeft 0.3s ease;
}

.gslideOutRight {
  -webkit-animation: gslideOutRight 0.3s ease;
  animation: gslideOutRight 0.3s ease;
}

.gslideInRight {
  -webkit-animation: gslideInRight 0.3s ease;
  animation: gslideInRight 0.3s ease;
}

.gzoomIn {
  -webkit-animation: gzoomIn 0.5s ease;
  animation: gzoomIn 0.5s ease;
}

.gzoomOut {
  -webkit-animation: gzoomOut 0.5s ease;
  animation: gzoomOut 0.5s ease;
}

@-webkit-keyframes lightboxLoader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes lightboxLoader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes gfadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes gfadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes gfadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes gfadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-webkit-keyframes gslideInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
  }
  to {
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes gslideInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
  }
  to {
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes gslideOutLeft {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
    opacity: 0;
    visibility: hidden;
  }
}
@keyframes gslideOutLeft {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
    opacity: 0;
    visibility: hidden;
  }
}
@-webkit-keyframes gslideInRight {
  from {
    opacity: 0;
    visibility: visible;
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes gslideInRight {
  from {
    opacity: 0;
    visibility: visible;
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes gslideOutRight {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
    opacity: 0;
  }
}
@keyframes gslideOutRight {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
    opacity: 0;
  }
}
@-webkit-keyframes gzoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 1;
  }
}
@keyframes gzoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes gzoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes gzoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@media (min-width: 769px) {
  .glightbox-container .ginner-container {
    width: auto;
    height: auto;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .glightbox-container .ginner-container.desc-top .gslide-description {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }
  .glightbox-container .ginner-container.desc-top .gslide-image,
  .glightbox-container .ginner-container.desc-top .gslide-image img {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }
  .glightbox-container .ginner-container.desc-left .gslide-description {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }
  .glightbox-container .ginner-container.desc-left .gslide-image {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }
  .gslide-image img {
    max-height: 97vh;
    max-width: 100%;
  }
  .gslide-image img.zoomable {
    cursor: zoom-in;
  }
  .zoomed .gslide-image img.zoomable {
    cursor: grab;
  }
  .gslide-inline {
    max-height: 95vh;
  }
  .gslide-external {
    max-height: 100vh;
  }
  .gslide-description.description-left,
  .gslide-description.description-right {
    max-width: 275px;
  }
  .glightbox-open {
    height: auto;
  }
  .goverlay {
    background: rgba(0, 0, 0, 0.92);
  }
  .glightbox-clean .gslide-media {
    -webkit-box-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);
    box-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);
  }
  .glightbox-clean .description-left .gdesc-inner,
  .glightbox-clean .description-right .gdesc-inner {
    position: absolute;
    height: 100%;
    overflow-y: auto;
  }
  .glightbox-clean .gprev,
  .glightbox-clean .gnext,
  .glightbox-clean .gclose {
    background-color: rgba(0, 0, 0, 0.32);
  }
  .glightbox-clean .gprev:hover,
  .glightbox-clean .gnext:hover,
  .glightbox-clean .gclose:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
  .glightbox-clean .gprev {
    top: 45%;
  }
  .glightbox-clean .gnext {
    top: 45%;
  }
}
@media (min-width: 992px) {
  .glightbox-clean .gclose {
    opacity: 0.7;
    right: 20px;
  }
}
@media screen and (max-height: 420px) {
  .goverlay {
    background: #000;
  }
}
table.CalendarListView {
  max-width: 100%;
  width: 100%;
}
table.CalendarListView tbody {
  line-height: 1.2;
}
table.CalendarListView tbody > tr {
  max-width: 100%;
  width: 100%;
  display: grid;
  grid-template-columns: 12% auto; /* Last column 'auto' takes remaining space */
  padding: 15px 0;
  border-bottom: 0 !important;
}
@media screen and (max-width: 768px) {
  table.CalendarListView tbody > tr {
    grid-template-columns: 20% auto;
  }
}
table.CalendarListView tbody > tr td.date, table.CalendarListView tbody > tr th.date {
  grid-column: 1;
}
table.CalendarListView tbody > tr td.gig, table.CalendarListView tbody > tr th.gig {
  grid-column: 2;
}
@media screen and (max-width: 768px) {
  table.CalendarListView tbody > tr td, table.CalendarListView tbody > tr th {
    padding: 5px;
  }
}
@media screen and (max-width: 450px) {
  table.CalendarListView tbody > tr td, table.CalendarListView tbody > tr th {
    font-size: 13px;
  }
}
@media screen and (max-width: 350px) {
  table.CalendarListView tbody > tr td, table.CalendarListView tbody > tr th {
    font-size: 12px;
    padding: 3px;
  }
}
table.CalendarListView tbody > tr td.gig {
  display: flex;
}
@media screen and (max-width: 768px) {
  table.CalendarListView tbody > tr td.gig {
    display: table-cell;
  }
}
table.CalendarListView tbody > tr td.gig div.text {
  flex-grow: 1; /* This ensures that .text div take up the remaining space */
  text-align: justify;
  hyphens: auto;
}
table.CalendarListView tbody > tr td.gig div.text a {
  color: inherit;
  text-decoration: underline !important;
}
table.CalendarListView tbody > tr td.gig div.text a:link, table.CalendarListView tbody > tr td.gig div.text a:visited {
  color: inherit;
  text-decoration: underline !important;
}
table.CalendarListView tbody > tr td.gig div.text a:hover {
  color: inherit;
  text-decoration: underline !important;
}
table.CalendarListView tbody > tr td.gig div.text a:focus, table.CalendarListView tbody > tr td.gig div.text a:active {
  color: inherit;
}
table.CalendarListView tbody > tr td.gig div.text div.otherfiles a {
  display: block;
}
table.CalendarListView tbody > tr td.gig div.text div.loc {
  font-style: normal;
}
table.CalendarListView tbody > tr td.gig div.text div.loc a {
  font-style: italic;
}
table.CalendarListView tbody > tr td.gig div.text div.loc .locationMain {
  font-weight: bold;
  font-style: normal;
}
table.CalendarListView tbody > tr td.gig div.text div.loc .dashicons-location {
  position: relative;
  left: -5px;
}
table.CalendarListView tbody > tr td.gig div.text div.title {
  font-weight: normal;
  font-style: normal;
}
table.CalendarListView tbody > tr td.gig div.text div:not(:first-child) {
  margin-top: 10px;
}
table.CalendarListView tbody > tr td.gig div.imgs {
  margin-left: 10px;
}
@media screen and (max-width: 768px) {
  table.CalendarListView tbody > tr td.gig div.imgs {
    display: flex; /* Flexbox layout */
    flex-direction: row; /* Align items in a row */
    align-items: flex-start; /* Align items at the top */
    margin-left: 0;
    margin-top: 15px;
    left: 0;
  }
}
table.CalendarListView tbody > tr td.gig div.imgs a.glightbox {
  display: block;
  /*white-space: nowrap;*/
}
@media screen and (max-width: 768px) {
  table.CalendarListView tbody > tr td.gig div.imgs a.glightbox {
    display: inline-block;
  }
}
table.CalendarListView tbody > tr td.gig div.imgs a.glightbox:not(:first-child) {
  margin-top: 10px;
}
@media screen and (max-width: 768px) {
  table.CalendarListView tbody > tr td.gig div.imgs a.glightbox:not(:first-child) {
    margin-top: 0;
    margin-left: 10px;
  }
}
table.CalendarListView tbody > tr td.gig div.imgs a.glightbox img {
  width: 100px;
  min-width: 100px;
}

#headers_container #mainheader_container #mainheader_wrapper #logo-and-text {
  padding: 1vw 0;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text #logo {
  display: none;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-title-text a {
  color: #ECDCD3;
  text-decoration: none;
  color: #ECDCD3;
  text-decoration: none !important;
  font-family: "Special Elite", "Times New Roman", Times, serif;
  text-shadow: 1px 1px 1px #000;
  font-size: 30px;
  font-weight: 900;
  text-transform: uppercase;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-title-text a:link, #headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-title-text a:visited {
  color: #ECDCD3;
  text-decoration: none;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-title-text a:hover {
  color: #ECDCD3 !important;
  text-decoration: none;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-title-text a:focus, #headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-title-text a:active {
  color: #ECDCD3;
}
#headers_container #mainheader_container #mainheader_wrapper #logo-and-text #text #site-description-text {
  font-size: 18px;
  opacity: 0.8;
  color: #fff;
  text-transform: uppercase;
  margin: -6px 0 0 0;
}

#post_container.post-30 .fl-module-gallery a img,
#post_container.post-30 .fl-photo-content a img,
.d_createGallery .d_createGallery__item a img {
  border: 1px solid #e2e2e2;
}

#row_socials {
  margin-bottom: 4vw !important;
}
@media screen and (max-width: 768px) {
  #row_socials {
    margin-bottom: 12vw !important;
  }
}
#row_socials .fl-row-fixed-width {
  max-width: 540px;
}
#row_socials .fl-col-group {
  display: flex;
  align-items: center;
  justify-content: center;
}
#row_socials .fl-col-group .fl-col {
  padding: 0 !important;
  text-align: center;
  margin: 0 1vw !important;
}
#row_socials .fl-col-group .fl-col .fl-node-content {
  text-align: center;
}
@media screen and (max-width: 400px) {
  #row_socials .fl-col-group .fl-col .fl-node-content .fl-module-icon .fl-icon i, #row_socials .fl-col-group .fl-col .fl-node-content .fl-module-icon .fl-icon i::before {
    font-size: 40px;
  }
}
#row_socials .fl-col-group .fl-col .fl-node-content .fl-module-icon .fl-icon i:hover, #row_socials .fl-col-group .fl-col .fl-node-content .fl-module-icon .fl-icon a:hover {
  text-decoration: none !important;
  text-decoration-color: #fff;
}
#row_socials .fl-col-group .fl-col .fl-node-content .fl-module-html {
  font-size: 13px;
  line-height: 0.8;
}
@media screen and (max-width: 400px) {
  #row_socials .fl-col-group .fl-col .fl-node-content .fl-module-html {
    font-size: 11px;
    line-height: 0.5;
  }
}

#wprmenu_bar.d_mod #d_wprm_menu_title {
  display: none !important;
}

h1, .h1, h2, .h2 {
  text-transform: uppercase !important;
  margin-top: -0.05em;
}

h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  font-family: "Inconsolata", "Arial", "Helvetica", "Liberation Sans", Tahoma, Geneva, sans-serif;
}

@media screen and (max-width: 550px) {
  body.is_front_page #body_container #fb_col {
    margin-right: 25px;
    overflow: hidden;
  }
}

@media screen and (max-width: 768px) {
  body.page-id-30 .fl-photo {
    margin-bottom: 3vw;
  }
}
/*# sourceMappingURL=style.debug.css.map */

/* Grid
------------------------------------------------------ */

.fl-builder-content *,
.fl-builder-content *:before,
.fl-builder-content *:after {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.fl-row:before,
.fl-row:after,
.fl-row-content:before,
.fl-row-content:after,
.fl-col-group:before,
.fl-col-group:after,
.fl-col:before,
.fl-col:after,
.fl-module:before,
.fl-module:after,
.fl-module-content:before,
.fl-module-content:after {
	display: table;
	content: " ";
}
.fl-row:after,
.fl-row-content:after,
.fl-col-group:after,
.fl-col:after,
.fl-module:after,
.fl-module-content:after {
	clear: both;
}
.fl-clear {
	clear: both;
}

/* Utilities
------------------------------------------------------ */

.fl-clearfix:before,
.fl-clearfix:after {
	display: table;
	content: " ";
}
.fl-clearfix:after {
	clear: both;
}
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}

/* Rows
------------------------------------------------------ */

.fl-row,
.fl-row-content {
	margin-left: auto;
	margin-right: auto;
	min-width: 0;
}
.fl-row-content-wrap {
	position: relative;
}

/* Photo Bg */
.fl-builder-mobile .fl-row-bg-photo .fl-row-content-wrap {
	background-attachment: scroll;
}

/* Video and Embed Code Bg */
.fl-row-bg-video,
.fl-row-bg-video .fl-row-content,
.fl-row-bg-embed,
.fl-row-bg-embed .fl-row-content {
	position: relative;
}

.fl-row-bg-video .fl-bg-video,
.fl-row-bg-embed .fl-bg-embed-code {
	bottom: 0;
	left: 0;
	overflow: hidden;
	position: absolute;
	right: 0;
	top: 0;
}

.fl-row-bg-video .fl-bg-video video,
.fl-row-bg-embed .fl-bg-embed-code video {
	bottom: 0;
	left: 0px;
	max-width: none;
	position: absolute;
	right: 0;
	top: 0px;
}
.fl-row-bg-video .fl-bg-video video {
	min-width: 100%;
	min-height: 100%;
	width: auto;
	height: auto;
}
.fl-row-bg-video .fl-bg-video iframe,
.fl-row-bg-embed .fl-bg-embed-code iframe {
	pointer-events: none;
	width: 100vw;
  	height: 56.25vw; /* Given a 16:9 aspect ratio, 9/16*100 = 56.25 */
  	max-width: none;
  	min-height: 100vh;
  	min-width: 177.77vh; /* Given a 16:9 aspect ratio, 16/9*100 = 177.77 */
  	position: absolute;
  	top: 50%;
  	left: 50%;
  	transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%); /* IE 9 */
	-webkit-transform: translate(-50%, -50%); /* Chrome, Safari, Opera */
}
.fl-bg-video-fallback {
	background-position: 50% 50%;
	background-repeat: no-repeat;
	background-size: cover;
	bottom: 0px;
	left: 0px;
	position: absolute;
	right: 0px;
	top: 0px;
}

/* Slideshow Bg */
.fl-row-bg-slideshow,
.fl-row-bg-slideshow .fl-row-content {
	position: relative;
}
.fl-row .fl-bg-slideshow {
	bottom: 0;
	left: 0;
	overflow: hidden;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 0;
}
.fl-builder-edit .fl-row .fl-bg-slideshow * {
	bottom: 0;
	height: auto !important;
	left: 0;
	position: absolute !important;
	right: 0;
	top: 0;
}

/* Row Bg Overlay */
.fl-row-bg-overlay .fl-row-content-wrap:after {
	border-radius: inherit;
	content: '';
	display: block;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 0;
}
.fl-row-bg-overlay .fl-row-content {
	position: relative;
	z-index: 1;
}

/* Full Height Rows */
.fl-row-default-height .fl-row-content-wrap,
.fl-row-custom-height .fl-row-content-wrap {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	min-height: 100vh;
}
.fl-row-overlap-top .fl-row-content-wrap {
	display: -webkit-inline-box;
	display: -webkit-inline-flex;
	display: -moz-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	width: 100%;
}
.fl-row-default-height .fl-row-content-wrap,
.fl-row-custom-height .fl-row-content-wrap {
	min-height: 0;
}

.fl-row-default-height .fl-row-content,
.fl-row-full-height .fl-row-content,
.fl-row-custom-height .fl-row-content {
  -webkit-box-flex: 1 1 auto;
  	 -moz-box-flex: 1 1 auto;
  	  -webkit-flex: 1 1 auto;
  		  -ms-flex: 1 1 auto;
  			  flex: 1 1 auto;
}
.fl-row-default-height .fl-row-full-width.fl-row-content,
.fl-row-full-height .fl-row-full-width.fl-row-content,
.fl-row-custom-height .fl-row-full-width.fl-row-content {
	max-width: 100%;
	width: 100%;
}

/* Full height align center */
.fl-row-default-height.fl-row-align-center .fl-row-content-wrap,
.fl-row-full-height.fl-row-align-center .fl-row-content-wrap,
.fl-row-custom-height.fl-row-align-center .fl-row-content-wrap {
	align-items: center;
	justify-content: center;
	-webkit-align-items: center;
	-webkit-box-align: center;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-align: center;
	-ms-flex-pack: center;
}

/* Full height align bottom */
.fl-row-default-height.fl-row-align-bottom .fl-row-content-wrap,
.fl-row-full-height.fl-row-align-bottom .fl-row-content-wrap,
.fl-row-custom-height.fl-row-align-bottom .fl-row-content-wrap {
	align-items: flex-end;
	justify-content: flex-end;
	-webkit-align-items: flex-end;
	-webkit-justify-content: flex-end;
	-webkit-box-align: end;
	-webkit-box-pack: end;
	-ms-flex-align: end;
	-ms-flex-pack: end;
}

/* Column Groups
------------------------------------------------------ */
.fl-col-group-equal-height {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
}
.fl-col-group-equal-height.fl-col-group-has-child-loading {
	flex-wrap: nowrap;
}
.fl-col-group-equal-height .fl-col,
.fl-col-group-equal-height .fl-col-content {
	display: flex;
	flex: 1 1 auto;
}
.fl-col-group-equal-height .fl-col-content {
	flex-direction: column;
	flex-shrink: 1;
	min-width: 1px;
	max-width: 100%;
	width: 100%;
}
.fl-col-group-equal-height:before,
.fl-col-group-equal-height .fl-col:before,
.fl-col-group-equal-height .fl-col-content:before,
.fl-col-group-equal-height:after,
.fl-col-group-equal-height .fl-col:after,
.fl-col-group-equal-height .fl-col-content:after{
	content: none;
}

/* Equal height align top */
.fl-col-group-nested.fl-col-group-equal-height.fl-col-group-align-top .fl-col-content,
.fl-col-group-equal-height.fl-col-group-align-top .fl-col-content {
	justify-content: flex-start;
}

/* Equal height align center */
.fl-col-group-nested.fl-col-group-equal-height.fl-col-group-align-center .fl-col-content,
.fl-col-group-equal-height.fl-col-group-align-center .fl-col-content {
	align-items: center;
	justify-content: center;
}

/* Equal height align bottom */
.fl-col-group-nested.fl-col-group-equal-height.fl-col-group-align-bottom .fl-col-content,
.fl-col-group-equal-height.fl-col-group-align-bottom .fl-col-content {
	justify-content: flex-end;
}

.fl-col-group-equal-height.fl-col-group-align-center .fl-module,
.fl-col-group-equal-height.fl-col-group-align-center .fl-col-group {
	width: 100%;
}

/* Columns
------------------------------------------------------ */

.fl-col {
	float: left;
	min-height: 1px;
}

/* Column Bg Overlay */
.fl-col-bg-overlay .fl-col-content {
	position: relative;
}
.fl-col-bg-overlay .fl-col-content:after {
	border-radius: inherit;
	content: '';
	display: block;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 0;
}
.fl-col-bg-overlay .fl-module {
	position: relative;
	z-index: 2;
}

/* Modules
------------------------------------------------------ */

.fl-module img {
	max-width: 100%;
}

/* Module Templates
------------------------------------------------------ */

.fl-builder-module-template {
	margin: 0 auto;
	max-width: 1100px;
	padding: 20px;
}

/* Buttons
------------------------------------------------------ */

.fl-builder-content a.fl-button,
.fl-builder-content a.fl-button:visited {
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	display: inline-block;
	font-size: 16px;
	font-weight: normal;
	line-height: 18px;
	padding: 12px 24px;
	text-decoration: none;
	text-shadow: none;
}
.fl-builder-content .fl-button:hover {
	text-decoration: none;
}
.fl-builder-content .fl-button:active {
	position: relative;
	top: 1px;
}
.fl-builder-content .fl-button-width-full .fl-button {
	display: block;
	text-align: center;
}
.fl-builder-content .fl-button-width-custom .fl-button {
	display: inline-block;
	text-align: center;
	max-width: 100%;
}
.fl-builder-content .fl-button-left {
	text-align: left;
}
.fl-builder-content .fl-button-center {
	text-align: center;
}
.fl-builder-content .fl-button-right {
	text-align: right;
}
.fl-builder-content .fl-button i {
	font-size: 1.3em;
	height: auto;
	margin-right:8px;
	vertical-align: middle;
	width: auto;
}
.fl-builder-content .fl-button i.fl-button-icon-after {
	margin-left: 8px;
	margin-right: 0;
}
.fl-builder-content .fl-button-has-icon .fl-button-text {
	vertical-align: middle;
}

/* Icons
------------------------------------------------------ */

.fl-icon-wrap {
	display: inline-block;
}
.fl-icon {
	display: table-cell;
	vertical-align: middle;
}
.fl-icon a {
	text-decoration: none;
}
.fl-icon i {
	float: right;
	height: auto;
	width: auto;
}
.fl-icon i:before {
	border: none !important;
	height: auto;
	width: auto;
}
.fl-icon-text {
	display: table-cell;
	text-align: left;
	padding-left: 15px;
	vertical-align: middle;
}
.fl-icon-text-empty {
	display: none;
}
.fl-icon-text *:last-child {
	margin: 0 !important;
	padding: 0 !important;
}
.fl-icon-text a {
	text-decoration: none;
}
.fl-icon-text span {
	display: block;
}
.fl-icon-text span.mce-edit-focus {
	min-width: 1px;
}

/* Photos
------------------------------------------------------ */

.fl-photo {
	line-height: 0;
	position: relative;
}
.fl-photo-align-left {
	text-align: left;
}
.fl-photo-align-center {
	text-align: center;
}
.fl-photo-align-right {
	text-align: right;
}
.fl-photo-content {
	display: inline-block;
	line-height: 0;
	position: relative;
	max-width: 100%;
}
.fl-photo-img-svg {
	width: 100%;
}
.fl-photo-content img {
	display: inline;
	height: auto;
	max-width: 100%;
}
.fl-photo-crop-circle img {
	-webkit-border-radius: 100%;
	-moz-border-radius: 100%;
	border-radius: 100%;
}
.fl-photo-caption {
	font-size: 13px;
	line-height: 18px;
	overflow: hidden;
	text-overflow: ellipsis;
}
.fl-photo-caption-below {
	padding-bottom: 20px;
	padding-top: 10px;
}
.fl-photo-caption-hover {
	background: rgba(0,0,0,0.7);
	bottom: 0;
	color: #fff;
	left: 0;
	opacity: 0;
	filter: alpha(opacity = 0);
	padding: 10px 15px;
	position: absolute;
	right: 0;
	-webkit-transition:opacity 0.3s ease-in;
	-moz-transition:opacity 0.3s ease-in;
	transition:opacity 0.3s ease-in;
}
.fl-photo-content:hover .fl-photo-caption-hover {
	opacity: 100;
	filter: alpha(opacity = 100);
}

/* Pagination
------------------------------------------------------ */

.fl-builder-pagination,
.fl-builder-pagination-load-more {
	padding: 40px 0;
}
.fl-builder-pagination ul.page-numbers {
	list-style: none;
	margin: 0;
	padding: 0;
	text-align: center;
}
.fl-builder-pagination li {
	display: inline-block;
	list-style: none;
	margin: 0;
	padding: 0;
}
.fl-builder-pagination li a.page-numbers,
.fl-builder-pagination li span.page-numbers {
	border: 1px solid #e6e6e6;
	display: inline-block;
	padding: 5px 10px;
	margin: 0 0 5px;
}
.fl-builder-pagination li a.page-numbers:hover,
.fl-builder-pagination li span.current {
	background: #f5f5f5;
	text-decoration: none;
}

/* Slideshows
------------------------------------------------------ */

.fl-slideshow,
.fl-slideshow * {
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;

}
.fl-slideshow .fl-slideshow-image img {
	max-width: none !important;
}
.fl-slideshow-social {
	line-height: 0 !important;
}
.fl-slideshow-social * {
	margin: 0 !important;
}

/* Sliders
------------------------------------------------------ */

.fl-builder-content .bx-wrapper .bx-viewport {
	background: transparent;
	border: none;
	box-shadow: none;
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	left: 0;
}

/* Lightbox
------------------------------------------------------ */

.mfp-wrap button.mfp-arrow,
.mfp-wrap button.mfp-arrow:active,
.mfp-wrap button.mfp-arrow:hover,
.mfp-wrap button.mfp-arrow:focus {
	background: transparent !important;
	border: none !important;
	outline: none;
	position: absolute;
	top: 50%;
	box-shadow: none !important;
	-moz-box-shadow: none !important;
	-webkit-box-shadow: none !important;
}
.mfp-wrap .mfp-close,
.mfp-wrap .mfp-close:active,
.mfp-wrap .mfp-close:hover,
.mfp-wrap .mfp-close:focus {
	background: transparent !important;
	border: none !important;
	outline: none;
	position: absolute;
	top: 0;
	box-shadow: none !important;
	-moz-box-shadow: none !important;
	-webkit-box-shadow: none !important;
}
.admin-bar .mfp-wrap .mfp-close,
.admin-bar .mfp-wrap .mfp-close:active,
.admin-bar .mfp-wrap .mfp-close:hover,
.admin-bar .mfp-wrap .mfp-close:focus {
	top: 32px!important;
}
img.mfp-img {
	padding: 0;
}
.mfp-counter {
	display: none;
}

.mfp-wrap .mfp-preloader.fa {
	font-size: 30px;
}

/* Form Fields
------------------------------------------------------ */

.fl-form-field {
	margin-bottom: 15px;
}
.fl-form-field input.fl-form-error {
	border-color: #DD6420;
}
.fl-form-error-message {
	clear: both;
	color: #DD6420;
	display: none;
	padding-top: 8px;
	font-size: 12px;
	font-weight: lighter;
}
.fl-form-button-disabled {
	opacity: 0.5;
}

/* Animations
------------------------------------------------------ */

.fl-animation {
	opacity: 0;
}
.fl-builder-preview .fl-animation,
.fl-builder-edit .fl-animation,
.fl-animated {
	opacity: 1;
}
.fl-animated {
	animation-fill-mode: both;
	-webkit-animation-fill-mode: both;
}

/* Button Icon Animation */
.fl-button.fl-button-icon-animation i {
	width: 0 !important;
	opacity: 0;
	-ms-filter: "alpha(opacity=0)";
	transition: all 0.2s ease-out;
	-webkit-transition: all 0.2s ease-out;
}
.fl-button.fl-button-icon-animation:hover i {
	opacity: 1! important;
    -ms-filter: "alpha(opacity=100)";
}
.fl-button.fl-button-icon-animation i.fl-button-icon-after {
	margin-left: 0px !important;
}
.fl-button.fl-button-icon-animation:hover i.fl-button-icon-after {
	margin-left: 10px !important;
}
.fl-button.fl-button-icon-animation i.fl-button-icon-before {
	margin-right: 0 !important;
}
.fl-button.fl-button-icon-animation:hover i.fl-button-icon-before {
	margin-right: 20px !important;
    margin-left: -10px;
}

/* Templates
------------------------------------------------------ */

.single:not(.woocommerce).single-fl-builder-template .fl-content {
	width: 100%;
}

/* Shapes & Patterns
------------------------------------------------------- */
.fl-builder-layer {
	position: absolute;
	top:0;
	left:0;
	right: 0;
	bottom: 0;
	z-index: 0;
	pointer-events: none;
	overflow: hidden;
}
.fl-builder-shape-layer {
	z-index: 0;
}
.fl-builder-shape-layer.fl-builder-bottom-edge-layer {
	z-index: 1;
}
.fl-row-bg-overlay .fl-builder-shape-layer {
	z-index: 1;
}
.fl-row-bg-overlay .fl-builder-shape-layer.fl-builder-bottom-edge-layer {
	z-index: 2;
}
.fl-row-has-layers .fl-row-content {
	z-index: 1;
}
.fl-row-bg-overlay .fl-row-content {
	z-index: 2;
}

.fl-builder-layer > * {
	display: block;
	position: absolute;
	top:0;
	left:0;
	width: 100%;
}
.fl-builder-layer + .fl-row-content {
	position: relative;
}
.fl-builder-layer .fl-shape {
	fill: #aaa;
	stroke: none;
	stroke-width: 0;
	width:100%;
}
/**
Fix ipad parallax issue on safari
https://core.trac.wordpress.org/ticket/48802
https://core.trac.wordpress.org/ticket/49285
https://github.com/WordPress/gutenberg/issues/17718
*/
@supports (-webkit-touch-callout: inherit) {
  .fl-row.fl-row-bg-parallax .fl-row-content-wrap,
  .fl-row.fl-row-bg-fixed .fl-row-content-wrap {
    background-position: center !important;
    background-attachment: scroll !important;
  }
}

@supports (-webkit-touch-callout: none) {
	.fl-row.fl-row-bg-fixed .fl-row-content-wrap {
		background-position: center !important;
		background-attachment: scroll !important;
	}
}

/* Default button colors for non Beaver Builder themes.
------------------------------------------------------ */

.fl-builder-content a.fl-button,
.fl-builder-content a.fl-button:visited {
	background: #fafafa;
	border: 1px solid #ccc;
	color: #333;
}
.fl-builder-content a.fl-button *,
.fl-builder-content a.fl-button:visited * {
	color: #333;
}@media (max-width: 1200px) { /**
 * Styles needed for the large breakpoint.
 */
 }@media (max-width: 992px) { /* Columns
------------------------------------------------------ */

/* Reversed Responsive Stacking */
.fl-col-group.fl-col-group-medium-reversed {
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap-reverse;
	flex-wrap: wrap-reverse;
	flex-direction: row-reverse;
}
 }@media (max-width: 768px) { /* Rows
------------------------------------------------------ */

.fl-row-content-wrap {
	background-attachment: scroll !important;
}
.fl-row-bg-parallax .fl-row-content-wrap {
	background-attachment: scroll !important;
	background-position: center center !important;
}

/* Column Groups
------------------------------------------------------ */

/* Equal Heights */
.fl-col-group.fl-col-group-equal-height {
	display: block;
}
.fl-col-group.fl-col-group-equal-height.fl-col-group-custom-width {
	display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}

/* Reversed Responsive Stacking */
.fl-col-group.fl-col-group-responsive-reversed {
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap-reverse;
	flex-wrap: wrap-reverse;
	flex-direction: row-reverse;
}
.fl-col-group.fl-col-group-responsive-reversed .fl-col:not(.fl-col-small-custom-width) {
	flex-basis: 100%;
	width: 100% !important;
}
.fl-col-group.fl-col-group-medium-reversed:not(.fl-col-group-responsive-reversed) {
	display: unset;
	display: unset;
	-webkit-flex-wrap: unset;
	flex-wrap: unset;
	flex-direction: unset;
}

/* Columns
------------------------------------------------------ */

.fl-col {
	clear: both;
	float: none;
	margin-left: auto;
	margin-right: auto;
	width: auto !important;
}
.fl-col-small:not(.fl-col-small-full-width) {
	max-width: 400px;
}
.fl-block-col-resize {
	display:none;
}
/* Rows
------------------------------------------------------ */

.fl-row[data-node] .fl-row-content-wrap {
	margin: 0;
	padding-left: 0;
	padding-right: 0;
}
.fl-row[data-node] .fl-bg-video,
.fl-row[data-node] .fl-bg-slideshow {
	left: 0;
	right: 0;
}

/* Columns
------------------------------------------------------ */

.fl-col[data-node] .fl-col-content {
	margin: 0;
	padding-left: 0;
	padding-right: 0;
}
 }@media (min-width: 1201px) {
	html .fl-visible-large:not(.fl-visible-desktop),
	html .fl-visible-medium:not(.fl-visible-desktop),
	html .fl-visible-mobile:not(.fl-visible-desktop) {
		display: none;
	}
}

@media (min-width: 993px) and (max-width: 1200px) {
	html .fl-visible-desktop:not(.fl-visible-large),
	html .fl-visible-medium:not(.fl-visible-large),
	html .fl-visible-mobile:not(.fl-visible-large) {
		display: none;
	}
}

@media (min-width: 769px) and (max-width: 992px) {
	html .fl-visible-desktop:not(.fl-visible-medium),
	html .fl-visible-large:not(.fl-visible-medium),
	html .fl-visible-mobile:not(.fl-visible-medium) {
		display: none;
	}
}

@media (max-width: 768px) {
	html .fl-visible-desktop:not(.fl-visible-mobile),
	html .fl-visible-large:not(.fl-visible-mobile),
	html .fl-visible-medium:not(.fl-visible-mobile) {
		display: none;
	}
}
.fl-row-fixed-width {
	max-width: 1100px;
}
.fl-row-content-wrap {
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
	padding-top: 20px;
	padding-right: 20px;
	padding-bottom: 20px;
	padding-left: 20px;
}
.fl-module-content, :where(.fl-module:not(:has(> .fl-module-content))) {
	margin-top: 20px;
	margin-right: 20px;
	margin-bottom: 20px;
	margin-left: 20px;
}
.page .fl-post-header, .single-fl-builder-template .fl-post-header { display:none; }









.fl-node-cu8pr6qhwobj {
	width: 50%;
}




.fl-node-5f70ciodt61b {
	width: 100%;
}




.fl-node-7rw2vuiczs8f {
	width: 50%;
}

/* Start Global CSS */

/* End Global CSS */


/* Start Global Nodes CSS */

/* End Global Nodes CSS */


/* Start Layout CSS */

/* End Layout CSS */


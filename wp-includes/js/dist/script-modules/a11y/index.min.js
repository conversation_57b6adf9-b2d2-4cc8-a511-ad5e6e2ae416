var e={d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},t={};e.d(t,{m:()=>a,L:()=>o});let n="";function o(e,t){!function(){const e=document.getElementsByClassName("a11y-speak-region"),t=document.getElementById("a11y-speak-intro-text");for(let t=0;t<e.length;t++)e[t].textContent="";t&&t.setAttribute("hidden","hidden")}(),e=function(e){return e=e.replace(/<[^<>]+>/g," "),n===e&&(e+=" "),n=e,e}(e);const o=document.getElementById("a11y-speak-intro-text"),a=document.getElementById("a11y-speak-assertive"),r=document.getElementById("a11y-speak-polite");a&&"assertive"===t?a.textContent=e:r&&(r.textContent=e),o&&o.removeAttribute("hidden")}const a=()=>{};var r=t.m,s=t.L;export{r as setup,s as speak};
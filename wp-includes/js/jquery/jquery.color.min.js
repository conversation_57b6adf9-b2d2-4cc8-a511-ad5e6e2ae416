/*! jQuery Color v3.0.0 https://github.com/jquery/jquery-color | jquery.org/license */
!function(r,t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(r.jQuery)}(this,function(s,l){"use strict";var u,n={},t=n.toString,c=/^([\-+])=\s*(\d+\.?\d*)/,r=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(r){return[r[1],r[2],r[3],r[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(r){return[2.55*r[1],2.55*r[2],2.55*r[3],r[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?/,parse:function(r){return[parseInt(r[1],16),parseInt(r[2],16),parseInt(r[3],16),r[4]?(parseInt(r[4],16)/255).toFixed(2):1]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?/,parse:function(r){return[parseInt(r[1]+r[1],16),parseInt(r[2]+r[2],16),parseInt(r[3]+r[3],16),r[4]?(parseInt(r[4]+r[4],16)/255).toFixed(2):1]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(r){return[r[1],r[2]/100,r[3]/100,r[4]]}}],f=s.Color=function(r,t,n,e){return new s.Color.fn.parse(r,t,n,e)},p={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},d={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},h=s.each;function b(r){return null==r?r+"":"object"==typeof r?n[t.call(r)]||"object":typeof r}function g(r,t,n){var e=d[t.type]||{};return null==r?n||!t.def?null:t.def:(r=e.floor?~~r:parseFloat(r),e.mod?(r+e.mod)%e.mod:Math.min(e.max,Math.max(0,r)))}function m(e){var o=f(),a=o._rgba=[];return e=e.toLowerCase(),h(r,function(r,t){var n=t.re.exec(e),n=n&&t.parse(n),t=t.space||"rgba";if(n)return n=o[t](n),o[p[t].cache]=n[p[t].cache],a=o._rgba=n._rgba,!1}),a.length?("0,0,0,0"===a.join()&&s.extend(a,u.transparent),o):u[e]}function o(r,t,n){return 6*(n=(n+1)%1)<1?r+(t-r)*n*6:2*n<1?t:3*n<2?r+(t-r)*(2/3-n)*6:r}h(p,function(r,t){t.cache="_"+r,t.props.alpha={idx:3,type:"percent",def:1}}),s.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(r,t){n["[object "+t+"]"]=t.toLowerCase()}),(f.fn=s.extend(f.prototype,{parse:function(o,r,t,n){if(o===l)return this._rgba=[null,null,null,null],this;(o.jquery||o.nodeType)&&(o=s(o).css(r),r=l);var a=this,e=b(o),i=this._rgba=[];return r!==l&&(o=[o,r,t,n],e="array"),"string"===e?this.parse(m(o)||u._default):"array"===e?(h(p.rgba.props,function(r,t){i[t.idx]=g(o[t.idx],t)}),this):"object"===e?(o instanceof f?h(p,function(r,t){o[t.cache]&&(a[t.cache]=o[t.cache].slice())}):h(p,function(r,n){var e=n.cache;h(n.props,function(r,t){if(!a[e]&&n.to){if("alpha"===r||null==o[r])return;a[e]=n.to(a._rgba)}a[e][t.idx]=g(o[r],t,!0)}),a[e]&&s.inArray(null,a[e].slice(0,3))<0&&(null==a[e][3]&&(a[e][3]=1),n.from)&&(a._rgba=n.from(a[e]))}),this):void 0},is:function(r){var o=f(r),a=!0,i=this;return h(p,function(r,t){var n,e=o[t.cache];return e&&(n=i[t.cache]||t.to&&t.to(i._rgba)||[],h(t.props,function(r,t){if(null!=e[t.idx])return a=e[t.idx]===n[t.idx]})),a}),a},_space:function(){var n=[],e=this;return h(p,function(r,t){e[t.cache]&&n.push(r)}),n.pop()},transition:function(r,i){var r=(l=f(r))._space(),t=p[r],n=0===this.alpha()?f("transparent"):this,s=n[t.cache]||t.to(n._rgba),u=s.slice(),l=l[t.cache];return h(t.props,function(r,t){var n=t.idx,e=s[n],o=l[n],a=d[t.type]||{};null!==o&&(null===e?u[n]=o:(a.mod&&(o-e>a.mod/2?e+=a.mod:e-o>a.mod/2&&(e-=a.mod)),u[n]=g((o-e)*i+e,t)))}),this[r](u)},blend:function(r){var t,n,e;return 1===this._rgba[3]?this:(t=this._rgba.slice(),n=t.pop(),e=f(r)._rgba,f(s.map(t,function(r,t){return(1-n)*e[t]+n*r})))},toRgbaString:function(){var r="rgba(",t=s.map(this._rgba,function(r,t){return null!=r?r:2<t?1:0});return 1===t[3]&&(t.pop(),r="rgb("),r+t.join(", ")+")"},toHslaString:function(){var r="hsla(",t=s.map(this.hsla(),function(r,t){return null==r&&(r=2<t?1:0),r=t&&t<3?Math.round(100*r)+"%":r});return 1===t[3]&&(t.pop(),r="hsl("),r+t.join(", ")+")"},toHexString:function(r){var t=this._rgba.slice(),n=t.pop();return r&&t.push(~~(255*n)),"#"+s.map(t,function(r){return("0"+(r||0).toString(16)).substr(-2)}).join("")},toString:function(){return this.toRgbaString()}})).parse.prototype=f.fn,p.hsla.to=function(r){var t,n,e,o,a,i,s,u;return null==r[0]||null==r[1]||null==r[2]?[null,null,null,r[3]]:(t=r[0]/255,n=r[1]/255,e=r[2]/255,r=r[3],o=(u=Math.max(t,n,e))-(s=Math.min(t,n,e)),i=.5*(a=u+s),s=s===u?0:t===u?60*(n-e)/o+360:n===u?60*(e-t)/o+120:60*(t-n)/o+240,u=0==o?0:i<=.5?o/a:o/(2-a),[Math.round(s)%360,u,i,null==r?1:r])},p.hsla.from=function(r){var t,n,e;return null==r[0]||null==r[1]||null==r[2]?[null,null,null,r[3]]:(t=r[0]/360,e=r[1],n=r[2],r=r[3],e=2*n-(n=n<=.5?n*(1+e):n+e-n*e),[Math.round(255*o(e,n,t+1/3)),Math.round(255*o(e,n,t)),Math.round(255*o(e,n,t-1/3)),r])},h(p,function(s,r){var t=r.props,a=r.cache,i=r.to,u=r.from;f.fn[s]=function(r){var n,e,o;return i&&!this[a]&&(this[a]=i(this._rgba)),r===l?this[a].slice():(n=b(r),e="array"===n||"object"===n?r:arguments,o=this[a].slice(),h(t,function(r,t){r=e["object"===n?r:t.idx];null==r&&(r=o[t.idx]),o[t.idx]=g(r,t)}),u?((r=f(u(o)))[a]=o,r):f(o))},h(t,function(a,i){f.fn[a]||(f.fn[a]=function(r){var t=b(r),n="alpha"===a?this._hsla?"hsla":"rgba":s,e=this[n](),o=e[i.idx];return"undefined"===t?o:("function"===t&&(t=b(r=r.call(this,o))),null==r&&i.empty?this:("string"===t&&(t=c.exec(r))&&(r=o+parseFloat(t[2])*("+"===t[1]?1:-1)),e[i.idx]=r,this[n](e)))})})}),(f.hook=function(r){r=r.split(" ");h(r,function(r,e){s.cssHooks[e]={set:function(r,t){var n;"transparent"===t||"string"===b(t)&&!(n=m(t))||(t=(t=f(n||t)).toRgbaString()),r.style[e]=t}},s.fx.step[e]=function(r){r.colorInit||(r.start=f(r.elem,e),r.end=f(r.end),r.colorInit=!0),s.cssHooks[e].set(r.elem,r.start.transition(r.end,r.pos))}})})("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor"),s.cssHooks.borderColor={expand:function(n){var e={};return h(["Top","Right","Bottom","Left"],function(r,t){e["border"+t+"Color"]=n}),e}},u=s.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}});